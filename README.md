# IPP-UMA System

Investment Policy Platform - Unified Managed Account System

## 🚀 **HOW TO START THE PROTOTYPE**

### **Step 1: Start the Server**
**Double-click** → `run_ipp_uma_server.bat`

### **Step 2: Open in Browser**
**Go to** → `http://localhost:8000`

### **Step 3: Navigate**
**Click any page** from the sidebar navigation

---

## 🎯 **Main Features**

- **📊 Models Viewer** - View and manage UMA investment models
- **💰 Fee Calculator** - Calculate fees based on investment amounts and models
- **🔄 Blended Fee Calculator** - Calculate weighted average fees across multiple models
- **📄 IPS Generator** - Create Investment Policy Statements with risk assessment
- **📈 Model Holdings Viewer** - View detailed holdings for each model
- **📋 Pivot Table** - Interactive data analysis of UMA sales data
- **➕ Add New Model** - Add new investment models to the system

## 🌐 **Navigation**

The system uses a **responsive sidebar navigation** with:
- **Bilingual support** (English/French toggle)
- **Consistent layout** across all pages
- **Mobile-friendly** design

## 📁 **Core Files**

- **`run_ipp_uma_server.bat`** - **START HERE** - Launches the server
- **`ipp_uma_server.py`** - Main server application
- **`ipp_uma_models.db`** - SQLite database with models and fee data
- **`sidebar.css` & `sidebar.js`** - Shared navigation components
- **HTML files** - Web interface pages

---

## 📋 **Technical Details**

## Database Structure

The application uses a SQLite database with the following main tables:

### tblModels
- **ModelID**: Auto-increment primary key
- **EnglishDescription**: Text, required
- **FrenchDescription**: Text, required
- **EnglishCategory**: Text, required
- **FrenchCategory**: Text, required
- **StartDate**: Date, required
- **EndDate**: Date, optional
- **IsActive**: Boolean, default: True
- **FixedIncomePercentage**: Real (0-100)
- **EquityPercentage**: Real (0-100)
- **AlternativesPercentage**: Real (0-100)
- **ProgramType**: Text, fee program type

### tblFeePrograms
- **ProgramID**: Auto-increment primary key
- **EnglishName**: Text, program name in English
- **FrenchName**: Text, program name in French
- **Percentage**: Real, base percentage

### tblFeeTiers
- **TierID**: Auto-increment primary key
- **ProgramID**: Foreign key to tblFeePrograms
- **StartAmount**: Real, tier start amount
- **EndAmount**: Real, tier end amount
- **AdminCost**: Real, administrative cost
- **AdvisorFee**: Real, advisor fee

### tblModelCodes
- **ModelCodeID**: Auto-increment primary key
- **ModelID**: Foreign key to tblModels
- **ModelCode**: Text, unique model code
- **Description**: Text, model code description

### tblHoldings
- **HoldingID**: Auto-increment primary key
- **Symbol**: Text, holding symbol
- **Name**: Text, holding name
- **AssetClass**: Text, asset class
- **AssetSubclass**: Text, asset subclass
- **Currency**: Text, currency

### tblModelHoldings
- **ModelCodeID**: Foreign key to tblModelCodes
- **HoldingID**: Foreign key to tblHoldings
- **Weight**: Real, holding weight percentage

### tblUMASalesData
- **SalesID**: Auto-increment primary key
- **UMA_MODEL_CODE**: Text, UMA model code
- **UMA_MODEL_NAME**: Text, UMA model name
- **REP_CODE**: Text, representative code
- **ADVISOR_FULL_NAME**: Text, advisor full name
- **ADVISOR_PROVINCE**: Text, advisor province
- **ADVISOR_CITY**: Text, advisor city
- **PRODCT_SYMBL**: Text, product symbol
- **AUA**: Real, Assets Under Administration value

## Available Tools

The system includes several tools:

1. **Main Interface** (`sqlite_viewer.html`) - View and manage models
2. **Add Model** (`add_model.html`) - Add new models to the system
3. **Fee Calculator** (`fee_calculator.html`) - Calculate fees for individual models
4. **Blended Fee Calculator** (`blended_fee_calculator.html`) - Calculate weighted average fees for multiple models
5. **Model Holdings Viewer** (`model_holdings_viewer.html`) - View detailed holdings for each model
6. **UMA Pivot Table** (`uma_pivot_table.html`) - Interactive pivot table for UMA sales data analysis
7. **IPS Generator** (`ips_generator.html`) - Investment Policy Statement creation tool with risk assessment and model selection

## Data Import

### Model Data from Excel

To import model data from Excel:

```bash
python import_excel_data.py
```

This will import data from `docs/NBIN FINALV8 WITH CASH.xlsx` into the database.

### UMA Sales Data from CSV

To import UMA sales data from CSV:

```bash
python import_uma_sales_data.py
```

Or use the batch file:

```bash
import_uma_sales_data.bat
```

This will import data from `UMA sales data.csv` into the database for pivot table analysis.

## UMA Pivot Table Features

The UMA Pivot Table (`uma_pivot_table.html`) provides interactive data analysis capabilities:

- **Drag and Drop Interface**: Drag dimensions from the left panel to create custom pivot tables
- **Available Dimensions**: Model Code, Model Name, Advisor Name, Province, City, Rep Code, Product Symbol
- **Automatic Aggregation**: Automatically sums AUA (Assets Under Administration) values
- **Real-time Updates**: Generate pivot tables instantly by clicking "Generate Pivot Table"
- **Flexible Layout**: Add dimensions to rows and/or columns for different views
- **Summary Statistics**: Shows total records, columns, and grand total AUA

### Example Pivot Table Uses:
- View AUA by Province and City
- Analyze performance by Advisor and Model
- Compare product symbols across different regions
- Track rep code performance by model type

## IPS Generator Features

The IPS Generator (`ips_generator.html`) is a comprehensive Investment Policy Statement creation tool that follows the detailed Business Requirements Document (BRD) specifications:

### **5-Step Workflow Process:**

#### **Step 1: Client Information**
- Client name, account number, rep code, advisor name
- Account type selection (Individual, Joint, Corporate)
- Required field validation

#### **Step 2: Program Selection**
- **Program Types**: Discretionary PM Program (Managed), Elite UMA, Fee Based Program (Non-Discretionary)
- **Risk Assessment Methods**:
  - **Internal IPQ**: Complete built-in Investment Policy Questionnaire
  - **External IPQ**: Upload external questionnaire document
  - **Advisor Discussion**: Manual risk assessment with notes

#### **Step 3: Risk Assessment**
- **Internal IPQ**: Interactive questionnaire with scoring system
  - Age-based scoring
  - Investment experience assessment
  - Income level evaluation
  - Time horizon analysis
  - Risk reaction scenarios
- **External/Discussion**: Manual risk capacity and tolerance selection
- Automatic risk profile calculation based on scores

#### **Step 4: Model Selection**
- UMA model selection based on risk profile
- Asset allocation validation
- Fee calculation integration
- Model compatibility checking

#### **Step 5: Review & Generate**
- Complete IPS review
- PDF generation
- Compliance workflow integration
- Electronic signature preparation

### **Key Features:**

#### **Risk Profiling System**
- **Configurable Questionnaire**: Compliance team can add/update/delete questions
- **Bilingual Support**: English and French questions and answers
- **Scoring System**: Automatic calculation of risk capacity and tolerance scores
- **Risk Ranges**: Configurable Low/Medium/High ranges for different risk types
- **Override Capability**: Advisors can override calculated risk profiles with explanations

#### **Investment Strategy Mapping**
- **Program-Specific Strategies**: Different strategies for Managed, UMA, and Non-Managed programs
- **Asset Allocation Ranges**: Configurable min/max/target percentages for:
  - Cash & Equivalents
  - Fixed Income
  - Equity
  - Alternative Investments
- **Automatic Validation**: Ensures selected models match client risk profile

#### **UMA Integration**
- **Model Selection**: Integration with existing UMA models database
- **Fee Calculation**: Automatic blended fee calculation for selected models
- **Minimum Investment Validation**: Warnings for models below minimum thresholds
- **Non-Discretionary Sleeves**: Support for mixed discretionary/non-discretionary allocations

#### **Compliance & Workflow**
- **Approval Process**: Built-in workflow for compliance review and approval
- **Document Management**: Automatic IPS document generation and storage
- **Audit Trail**: Complete tracking of changes and approvals
- **Integration Ready**: Designed for integration with COB (Client Onboarding) system

#### **Business Rules & Validation**
- **Asset Allocation Validation**: Ensures allocations total 100%
- **Risk Profile Matching**: Validates model selection against client risk profile
- **Minimum Investment Checks**: Warns when model minimums are not met
- **Compliance Alerts**: Automatic notifications for approval requirements

### **Database Tables for IPS System:**

#### **tblIPQQuestions**
- Configurable questionnaire questions with bilingual support
- Risk capacity and tolerance impact flags
- Version control for questionnaire updates

#### **tblIPQAnswers**
- Multiple choice answers with scoring
- Bilingual answer text
- Sortable answer options

#### **tblRiskProfileRanges**
- Configurable risk level ranges (Low/Medium/High)
- Separate ranges for capacity and tolerance
- Score-based range definitions

#### **tblInvestmentStrategies**
- Program-specific investment strategies
- Risk profile to strategy mapping
- Asset allocation ranges and targets

#### **tblIPSDocuments**
- Complete IPS document storage
- Client information and selections
- Risk assessment results and overrides
- Approval status and workflow tracking

#### **tblIPSSelectedModels**
- Selected UMA models for each IPS
- Allocation percentages
- Discretionary/non-discretionary flags

### **Access Points:**
- **Direct URL**: `http://localhost:8000/ips_generator.html`
- **Main Interface**: Click "IPS Generator" button
- **Quick Start**: `start_ips_generator.bat` for easy startup
- **Setup Script**: `setup_ips_system.bat` for initial database setup
- **Server Restart**: `restart_server.bat` if experiencing connectivity issues

### **Current Implementation:**
The IPS Generator now uses **real database integration** with fallback support:
- **5 comprehensive IPQ questions** with professional scoring (hardcoded for reliability)
- **Dynamic UMA models** loaded from `tblModels` database table
- **Complete risk assessment** and strategy mapping
- **Program-specific workflows** for UMA, Managed, and Fee-Based programs
- **Automatic fallback** to sample data if database is unavailable

This provides real-time access to your actual model data while ensuring the system always works.

### **Model Data Integration:**
The IPS Generator dynamically loads UMA models from your `tblModels` database table:
- **Real-time Data**: Shows current active models from your database
- **Automatic Filtering**: Only displays active models (IsActive = 1)
- **Smart Categorization**: Includes Elite, Partner, and Strategic portfolio models
- **Asset Allocation Display**: Shows Fixed Income, Equity, and Alternatives percentages
- **Bilingual Support**: Displays both English and French model descriptions
- **Fallback Protection**: Uses sample models if database is unavailable

**Database Fields Used:**
- `ModelID` → Model identifier
- `EnglishDescription` / `FrenchDescription` → Model names
- `EnglishCategory` / `FrenchCategory` → Portfolio categories
- `FixedIncomePercentage` → Fixed income allocation
- `EquityPercentage` → Equity allocation
- `AlternativesPercentage` → Alternatives allocation
- `IsActive` → Active status filter
- `ProgramType` → Program classification

### **Robust Error Handling:**
The IPS Generator includes comprehensive error handling for maximum reliability:

#### **✅ IPS Generation (Step 5):**
- **API Success**: Saves to database and shows confirmation with document ID
- **API Failure**: Shows demo mode with complete IPS summary and clear notification
- **Graceful Degradation**: Always provides professional user experience
- **Detailed Summary**: Shows all collected data regardless of save status

#### **✅ Model Loading (Step 4):**
- **Database Success**: Loads real models from `tblModels` table
- **Database Failure**: Falls back to sample models automatically
- **Transparent Operation**: User experience remains consistent

#### **✅ User Experience:**
- **Professional Feedback**: Clear success/error messages with details
- **Progress Indicators**: Button states show operation progress
- **Data Preservation**: All entered data is maintained during errors
- **Recovery Options**: Easy restart and retry capabilities
