<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Holdings & Allocations - IPP-UMA System</title>



    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Main content with sidebar */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top bar */
        .top-bar {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px 35px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-bar h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .top-bar .language-toggle button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .top-bar .language-toggle button:hover {
            background: #3b82f6;
        }

        /* Language display - handled by sidebar.css */

        /* Page-specific styles */
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #6c757d;
            font-size: 1.1em;
        }

        .nav-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: #0056b3;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        select, input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .container button, .form-group button, .top-bar button:not(.lang-btn):not(.quick-action-btn) {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .container button:hover, .form-group button:hover, .top-bar button:not(.lang-btn):not(.quick-action-btn):hover {
            background-color: #218838;
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }
        .results {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .hidden {
            display: none;
        }
        .chart-container {
            margin-top: 30px;
            height: 300px;
        }
        .model-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f7ff;
            border-radius: 4px;
            border: 1px solid #b8daff;
        }
        .model-info h3 {
            margin-top: 0;
            color: #004085;
        }
        .asset-allocation {
            display: flex;
            margin-top: 10px;
        }
        .asset-bar {
            height: 20px;
            display: inline-block;
            text-align: center;
            color: white;
            font-size: 12px;
            line-height: 20px;
        }
        .fixed-income {
            background-color: #4e73df;
        }
        .equity {
            background-color: #1cc88a;
        }
        .alternatives {
            background-color: #f6c23e;
        }
        .cash {
            background-color: #36b9cc;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .error {
            color: #d9534f;
            padding: 15px;
            border: 1px solid #d9534f;
            border-radius: 4px;
            background-color: #f9f2f4;
            margin-bottom: 20px;
        }
    </style>

    <!-- Import sidebar CSS AFTER embedded styles to ensure proper precedence -->
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item active" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="add_model.html" class="nav-item create-action" id="addModelNav">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="window.location.href='add_model.html'" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Holdings & Allocations</span>
                <span class="french-text">Titres et Allocations</span>
            </h1>
            <div class="language-toggle">
                <button id="topEnglishBtn" class="active">English</button>
                <button id="topFrenchBtn">Français</button>
            </div>
        </div>

        <div class="container">
            <div class="header">
                <h1>IPP-UMA Model Holdings Viewer</h1>
                <p>View detailed holdings and asset allocation for UMA models</p>
            </div>

        <div class="form-group">
            <label for="modelSelect" id="modelSelectLabel">Select Model:</label>
            <select id="modelSelect" onchange="loadModelHoldings()">
                <option value="">-- Select a model --</option>
            </select>
        </div>

        <div id="modelInfo" class="model-info hidden">
            <!-- Model information will be displayed here -->
        </div>

        <div id="holdingsContainer" class="hidden">
            <h2 id="holdingsTitle">Model Holdings</h2>

            <div class="chart-container">
                <canvas id="holdingsChart"></canvas>
            </div>

            <table id="holdingsTable">
                <thead>
                    <tr>
                        <th id="symbolHeader">Symbol</th>
                        <th id="nameHeader">Name</th>
                        <th id="weightHeader">Weight (%)</th>
                        <th id="assetClassHeader">Asset Class</th>
                        <th id="currencyHeader">Currency</th>
                    </tr>
                </thead>
                <tbody id="holdingsTableBody">
                    <!-- Holdings data will be inserted here -->
                </tbody>
            </table>
        </div>

        <div id="loadingIndicator" class="loading hidden">
            <p id="loadingText">Loading model holdings...</p>
        </div>

        <div id="errorContainer" class="error hidden">
            <!-- Error messages will be displayed here -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let currentLanguage = 'en';
        let models = [];
        let currentModelHoldings = [];
        let holdingsChart = null;

        // Load data when page loads
        window.onload = function() {
            loadModels();
        };

        function loadModels() {
            fetch('/api/models')
                .then(response => response.json())
                .then(data => {
                    models = data;
                    populateModelDropdown();
                })
                .catch(error => {
                    console.error('Error loading models:', error);
                    showError(currentLanguage === 'en' ?
                        'Error loading models. Please try again.' :
                        'Erreur lors du chargement des modèles. Veuillez réessayer.');
                });
        }

        function populateModelDropdown() {
            const select = document.getElementById('modelSelect');

            // Clear existing options except the first one
            while (select.options.length > 1) {
                select.remove(1);
            }

            // Separate regular models and mutual funds
            const regularModels = models.filter(model => model.sourceType === 'model');
            const mutualFunds = models.filter(model => model.sourceType === 'mutual_fund');

            // Add regular models first
            if (regularModels.length > 0) {
                const regularGroup = document.createElement('optgroup');
                regularGroup.label = 'UMA Models';
                regularModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.modelId;
                    option.textContent = model.englishDescription;
                    regularGroup.appendChild(option);
                });
                select.appendChild(regularGroup);
            }

            // Add mutual funds with visual distinction
            if (mutualFunds.length > 0) {
                const fundsGroup = document.createElement('optgroup');
                fundsGroup.label = 'Mutual Funds';
                mutualFunds.forEach(fund => {
                    const option = document.createElement('option');
                    option.value = fund.modelId;
                    option.textContent = `🏦 ${fund.englishDescription} (${fund.fundCode})`;
                    fundsGroup.appendChild(option);
                });
                select.appendChild(fundsGroup);
            }
        }

        function loadModelHoldings() {
            const modelId = document.getElementById('modelSelect').value;

            if (!modelId) {
                document.getElementById('modelInfo').classList.add('hidden');
                document.getElementById('holdingsContainer').classList.add('hidden');
                return;
            }

            // Show loading indicator
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('errorContainer').classList.add('hidden');
            document.getElementById('modelInfo').classList.add('hidden');
            document.getElementById('holdingsContainer').classList.add('hidden');

            // Fetch model holdings
            fetch(`/api/model-holdings/${modelId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(currentLanguage === 'en' ?
                            'Error loading model holdings' :
                            'Erreur lors du chargement des titres du modèle');
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicator
                    document.getElementById('loadingIndicator').classList.add('hidden');

                    if (data.error) {
                        showError(data.error);
                        return;
                    }

                    // Store holdings data
                    currentModelHoldings = data.holdings || [];

                    // Display model info
                    displayModelInfo(data.model);

                    // Display holdings
                    displayHoldings(currentModelHoldings);
                })
                .catch(error => {
                    // Hide loading indicator
                    document.getElementById('loadingIndicator').classList.add('hidden');

                    console.error('Error loading model holdings:', error);
                    showError(error.message);
                });
        }

        function displayModelInfo(model) {
            if (!model) return;

            const modelInfo = document.getElementById('modelInfo');
            modelInfo.innerHTML = '';

            // Create model info content (always use English for simplicity)
            const modelName = document.createElement('h3');
            modelName.textContent = model.englishDescription;
            modelInfo.appendChild(modelName);

            const modelCategory = document.createElement('p');
            modelCategory.innerHTML = `<strong>Category:</strong> ${model.englishCategory}`;
            modelInfo.appendChild(modelCategory);

            const modelCode = document.createElement('p');
            modelCode.innerHTML = `<strong>Model Code:</strong> ${model.modelCode || 'N/A'}`;
            modelInfo.appendChild(modelCode);

            // Show model info
            modelInfo.classList.remove('hidden');
        }

        function displayHoldings(holdings) {
            const holdingsContainer = document.getElementById('holdingsContainer');
            const holdingsTableBody = document.getElementById('holdingsTableBody');

            // Clear existing holdings
            holdingsTableBody.innerHTML = '';

            if (!holdings || holdings.length === 0) {
                showError(currentLanguage === 'en' ?
                    'No holdings found for this model' :
                    'Aucun titre trouvé pour ce modèle');
                return;
            }

            // Sort holdings by weight (descending)
            holdings.sort((a, b) => b.weight - a.weight);

            // Add holdings to table
            holdings.forEach(holding => {
                const row = document.createElement('tr');

                // Symbol
                const symbolCell = document.createElement('td');
                symbolCell.textContent = holding.symbol;
                row.appendChild(symbolCell);

                // Name
                const nameCell = document.createElement('td');
                nameCell.textContent = holding.name;
                row.appendChild(nameCell);

                // Weight
                const weightCell = document.createElement('td');
                weightCell.textContent = `${holding.weight.toFixed(2)}%`;
                row.appendChild(weightCell);

                // Asset Class
                const assetClassCell = document.createElement('td');
                assetClassCell.textContent = holding.assetClass;
                row.appendChild(assetClassCell);

                // Currency
                const currencyCell = document.createElement('td');
                currencyCell.textContent = holding.currency;
                row.appendChild(currencyCell);

                holdingsTableBody.appendChild(row);
            });

            // Create chart
            createHoldingsChart(holdings);

            // Show holdings container
            holdingsContainer.classList.remove('hidden');
        }

        function createHoldingsChart(holdings) {
            const ctx = document.getElementById('holdingsChart').getContext('2d');

            // Destroy previous chart if it exists
            if (holdingsChart) {
                holdingsChart.destroy();
            }

            // Group holdings by asset class
            const assetClasses = {};
            holdings.forEach(holding => {
                const assetClass = holding.assetClass || 'Other';
                if (!assetClasses[assetClass]) {
                    assetClasses[assetClass] = 0;
                }
                assetClasses[assetClass] += holding.weight;
            });

            // Prepare data for chart
            const labels = Object.keys(assetClasses);
            const data = Object.values(assetClasses);
            const backgroundColor = labels.map(label => {
                if (label.toLowerCase().includes('fixed') || label.toLowerCase().includes('bond')) {
                    return 'rgba(78, 115, 223, 0.7)';
                } else if (label.toLowerCase().includes('equity')) {
                    return 'rgba(28, 200, 138, 0.7)';
                } else if (label.toLowerCase().includes('alternative')) {
                    return 'rgba(246, 194, 62, 0.7)';
                } else if (label.toLowerCase().includes('cash')) {
                    return 'rgba(54, 185, 204, 0.7)';
                } else {
                    return 'rgba(153, 102, 255, 0.7)';
                }
            });

            // Create chart
            holdingsChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: backgroundColor,
                        borderColor: backgroundColor.map(color => color.replace('0.7', '1')),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw.toFixed(2);
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateHoldingsChart() {
            if (holdingsChart) {
                // Update chart labels
                holdingsChart.options.plugins.tooltip.callbacks.label = function(context) {
                    const label = context.label || '';
                    const value = context.raw.toFixed(2);
                    return `${label}: ${value}%`;
                };

                holdingsChart.update();
            }
        }

        function showError(message) {
            const errorContainer = document.getElementById('errorContainer');
            errorContainer.textContent = message;
            errorContainer.classList.remove('hidden');
        }

        // Language toggle function
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fr' : 'en';
            updateLanguageDisplay();
        }

        function updateLanguageDisplay() {
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            if (currentLanguage === 'en') {
                englishElements.forEach(el => el.style.display = 'inline');
                frenchElements.forEach(el => el.style.display = 'none');
            } else {
                englishElements.forEach(el => el.style.display = 'none');
                frenchElements.forEach(el => el.style.display = 'inline');
            }
        }

        // Initialize language display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
        });

    </script>
    </div> <!-- Close main-content -->

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the sidebar functionality (HTML is now inline)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar functionality
            if (typeof initializeSidebar === 'function') {
                initializeSidebar();
            }
            if (typeof initializeLanguageSwitching === 'function') {
                initializeLanguageSwitching();
            }
            if (typeof setActiveNavItem === 'function') {
                setActiveNavItem();
            }
            if (typeof updateLanguageDisplay === 'function') {
                updateLanguageDisplay();
            }
        });
    </script>
</body>
</html>
