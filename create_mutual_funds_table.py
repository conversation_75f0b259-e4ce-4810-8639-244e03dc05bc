#!/usr/bin/env python3
import sqlite3
import os

DB_PATH = 'ipp_uma_models.db'

def create_mutual_funds_table():
    print(f"Creating tblMutualFunds table in: {DB_PATH}")
    
    if not os.path.exists(DB_PATH):
        print("Database file not found!")
        return
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Create the tblMutualFunds table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tblMutualFunds (
                FundID INTEGER PRIMARY KEY AUTOINCREMENT,
                FundCode TEXT NOT NULL UNIQUE,
                EnglishName TEXT NOT NULL,
                FrenchName TEXT NOT NULL,
                FundType TEXT,
                AssetClass TEXT,
                StartDate TEXT,
                EndDate TEXT,
                IsActive INTEGER DEFAULT 1,
                CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("✅ tblMutualFunds table created successfully")
        
        # Insert some sample data
        sample_funds = [
            ('FUND001', 'Canadian Equity Fund', 'Fonds d\'actions canadiennes', 'Equity', 'Canadian Equity', '2020-01-01', None, 1),
            ('FUND002', 'US Equity Fund', 'Fonds d\'actions américaines', 'Equity', 'US Equity', '2020-01-01', None, 1),
            ('FUND003', 'Bond Fund', 'Fonds d\'obligations', 'Fixed Income', 'Bonds', '2020-01-01', None, 1),
            ('FUND004', 'International Equity Fund', 'Fonds d\'actions internationales', 'Equity', 'International Equity', '2020-01-01', None, 1),
            ('FUND005', 'Money Market Fund', 'Fonds du marché monétaire', 'Money Market', 'Cash', '2020-01-01', None, 1)
        ]
        
        cursor.executemany("""
            INSERT INTO tblMutualFunds (FundCode, EnglishName, FrenchName, FundType, AssetClass, StartDate, EndDate, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, sample_funds)
        
        conn.commit()
        print(f"✅ Inserted {len(sample_funds)} sample mutual funds")
        
        # Verify the data
        cursor.execute("SELECT COUNT(*) FROM tblMutualFunds")
        count = cursor.fetchone()[0]
        print(f"📊 Total records in tblMutualFunds: {count}")
        
        # Show the data
        cursor.execute("SELECT FundCode, EnglishName, FrenchName, FundType FROM tblMutualFunds")
        records = cursor.fetchall()
        print("📋 Created mutual funds:")
        for record in records:
            print(f"   {record[0]}: {record[1]} / {record[2]} ({record[3]})")
        
        conn.close()
        print("✅ Mutual funds table setup completed successfully")
        
    except Exception as e:
        print(f"❌ Error creating table: {e}")

if __name__ == "__main__":
    create_mutual_funds_table()
