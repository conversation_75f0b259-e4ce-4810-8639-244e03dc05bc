#!/usr/bin/env python3
"""
Migration script to add SourceType columns to both tblModels and tblMutualFunds tables
"""
import sqlite3
import os

DB_PATH = 'ipp_uma_models.db'

def add_source_type_columns():
    """Add SourceType columns to both tables"""
    print(f"Adding SourceType columns to database: {DB_PATH}")
    
    if not os.path.exists(DB_PATH):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if SourceType column exists in tblModels
        cursor.execute("PRAGMA table_info(tblModels)")
        models_columns = [column[1] for column in cursor.fetchall()]
        
        if 'SourceType' not in models_columns:
            print("📝 Adding SourceType column to tblModels...")
            cursor.execute('ALTER TABLE tblModels ADD COLUMN SourceType TEXT DEFAULT "model"')
            
            # Update existing records to have 'model' as SourceType
            cursor.execute('UPDATE tblModels SET SourceType = "model" WHERE SourceType IS NULL')
            print("✅ SourceType column added to tblModels")
        else:
            print("ℹ️  SourceType column already exists in tblModels")
        
        # Check if SourceType column exists in tblMutualFunds
        cursor.execute("PRAGMA table_info(tblMutualFunds)")
        funds_columns = [column[1] for column in cursor.fetchall()]
        
        if 'SourceType' not in funds_columns:
            print("📝 Adding SourceType column to tblMutualFunds...")
            cursor.execute('ALTER TABLE tblMutualFunds ADD COLUMN SourceType TEXT DEFAULT "mutual_fund"')
            
            # Update existing records to have 'mutual_fund' as SourceType
            cursor.execute('UPDATE tblMutualFunds SET SourceType = "mutual_fund" WHERE SourceType IS NULL')
            print("✅ SourceType column added to tblMutualFunds")
        else:
            print("ℹ️  SourceType column already exists in tblMutualFunds")
        
        conn.commit()
        
        # Verify the changes
        print("\n📊 Verification:")
        
        # Check tblModels
        cursor.execute('SELECT COUNT(*) FROM tblModels WHERE SourceType = "model"')
        models_count = cursor.fetchone()[0]
        print(f"   tblModels with SourceType='model': {models_count}")
        
        # Check tblMutualFunds
        cursor.execute('SELECT COUNT(*) FROM tblMutualFunds WHERE SourceType = "mutual_fund"')
        funds_count = cursor.fetchone()[0]
        print(f"   tblMutualFunds with SourceType='mutual_fund': {funds_count}")
        
        # Show sample data
        print("\n📋 Sample data:")
        cursor.execute('SELECT ModelID, EnglishDescription, SourceType FROM tblModels LIMIT 3')
        for row in cursor.fetchall():
            print(f"   Model {row[0]}: {row[1]} (SourceType: {row[2]})")
            
        cursor.execute('SELECT FundID, EnglishName, SourceType FROM tblMutualFunds LIMIT 3')
        for row in cursor.fetchall():
            print(f"   Fund {row[0]}: {row[1]} (SourceType: {row[2]})")
        
        conn.close()
        print("\n✅ Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False

if __name__ == "__main__":
    add_source_type_columns()
