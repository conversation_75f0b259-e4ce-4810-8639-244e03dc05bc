#!/usr/bin/env python3
"""
Test script for IPS PDF generation
"""

import requests
import json

def test_ips_pdf_generation():
    """Test IPS PDF generation with sample data"""
    print("🧪 Testing IPS PDF Generation...")
    
    # Sample IPS data
    ips_data = {
        "clientName": "<PERSON>",
        "accountNumber": "ACC123456",
        "repCode": "REP001",
        "advisorName": "Jane Advisor",
        "accountType": "Individual",
        "programType": "UMA",
        "riskCapacityLevel": "Moderate",
        "riskToleranceLevel": "Moderate",
        "ipqMethod": "Questionnaire",
        "riskProfileOverride": False,
        "selectedModels": [
            {
                "modelId": 3,
                "allocation": 60,
                "model": {
                    "englishDescription": "Elite Core Balanced",
                    "frenchDescription": "Elite équilibrée de base",
                    "fixedIncome": 40,
                    "equity": 50,
                    "alternatives": 10
                }
            },
            {
                "modelId": 4,
                "allocation": 40,
                "model": {
                    "englishDescription": "Elite Growth",
                    "frenchDescription": "Elite Croissance",
                    "fixedIncome": 20,
                    "equity": 70,
                    "alternatives": 10
                }
            }
        ],
        "status": "Generated",
        "createdDate": "2025-06-03T13:00:00.000Z"
    }
    
    try:
        print("📤 Sending IPS data to server...")
        response = requests.post(
            'http://localhost:8000/api/ips-documents',
            headers={'Content-Type': 'application/json'},
            json=ips_data
        )
        
        if response.status_code == 200:
            print("✅ PDF generated successfully!")
            
            # Save the PDF file
            filename = f"test_ips_{ips_data['clientName'].replace(' ', '_')}.pdf"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"💾 PDF saved as: {filename}")
            print(f"📄 File size: {len(response.content)} bytes")
            
            return True
        else:
            print(f"❌ Failed to generate PDF: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_models_api():
    """Test that models API is working"""
    print("\n🧪 Testing Models API...")
    
    try:
        response = requests.get('http://localhost:8000/api/models')
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Models API working - {len(models)} models found")
            
            # Check for mutual funds
            mutual_funds = [m for m in models if m.get('modelType') == 'mutual_fund']
            regular_models = [m for m in models if m.get('modelType') == 'model']
            
            print(f"📊 Regular models: {len(regular_models)}")
            print(f"🏦 Mutual funds: {len(mutual_funds)}")
            
            # Show sample models
            if models:
                print("\n📋 Sample models:")
                for i, model in enumerate(models[:3]):
                    print(f"   {i+1}. {model.get('englishDescription')} (Type: {model.get('modelType', 'N/A')})")
            
            return True
        else:
            print(f"❌ Models API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 IPS PDF GENERATION TESTING")
    print("=" * 60)
    
    # Test 1: Models API
    models_ok = test_models_api()
    
    # Test 2: PDF Generation
    if models_ok:
        pdf_ok = test_ips_pdf_generation()
    else:
        print("\n⚠️  Skipping PDF test due to models API failure")
        pdf_ok = False
    
    print("\n" + "=" * 60)
    if models_ok and pdf_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Models API is working correctly")
        print("✅ PDF generation is working correctly")
        print("✅ IPS generator should work properly")
    else:
        print("❌ SOME TESTS FAILED")
        if not models_ok:
            print("❌ Models API is not working")
        if not pdf_ok:
            print("❌ PDF generation is not working")
    print("=" * 60)

if __name__ == '__main__':
    main()
