#!/usr/bin/env python3
"""
IPP-UMA System Server
Investment Policy Platform - Unified Managed Account System

This server provides API endpoints for managing investment models, mutual funds,
fee calculations, and IPS generation.
"""

import sqlite3
import json
from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
import os
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import io

# Configuration
DB_PATH = 'ipp_uma_models.db'
app = Flask(__name__)
CORS(app)

def get_db_connection():
    """Create a connection to the SQLite database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def get_models():
    """Get all models including mutual funds from unified tblModels table"""
    conn = get_db_connection()
    if not conn:
        return {"error": "Database connection failed"}

    try:
        cursor = conn.cursor()
        models = []

        # Check if new columns exist and add them if they don't
        cursor.execute("PRAGMA table_info(tblModels)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add missing columns if needed
        if 'MinimumInvestment' not in columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN MinimumInvestment REAL DEFAULT 0')
        if 'FundCode' not in columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN FundCode TEXT')
        if 'FundType' not in columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN FundType TEXT')
        if 'AssetClass' not in columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN AssetClass TEXT')
        if 'ModelType' not in columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN ModelType TEXT DEFAULT "model"')

        conn.commit()

        # Get all models (both investment models and mutual funds)
        cursor.execute("""
            SELECT
                ModelID,
                EnglishDescription,
                FrenchDescription,
                EnglishCategory,
                FrenchCategory,
                StartDate,
                EndDate,
                IsActive,
                COALESCE(FixedIncomePercentage, 0) as FixedIncomePercentage,
                COALESCE(EquityPercentage, 0) as EquityPercentage,
                COALESCE(AlternativesPercentage, 0) as AlternativesPercentage,
                COALESCE(ProgramType, 'UMA') as ProgramType,
                COALESCE(MinimumInvestment, 0) as MinimumInvestment,
                COALESCE(ModelType, 'model') as ModelType,
                FundCode,
                FundType,
                AssetClass
            FROM tblModels
            ORDER BY EnglishCategory, EnglishDescription
        """)

        model_rows = cursor.fetchall()

        for row in model_rows:
            models.append({
                "modelId": row['ModelID'],
                "englishDescription": row['EnglishDescription'],
                "frenchDescription": row['FrenchDescription'],
                "englishCategory": row['EnglishCategory'],
                "frenchCategory": row['FrenchCategory'],
                "startDate": row['StartDate'],
                "endDate": row['EndDate'],
                "isActive": bool(row['IsActive']),
                "fixedIncome": row['FixedIncomePercentage'] or 0,
                "equity": row['EquityPercentage'] or 0,
                "alternatives": row['AlternativesPercentage'] or 0,
                "programType": row['ProgramType'] or 'UMA',
                "minimumInvestment": row['MinimumInvestment'] or 0,
                "modelType": row['ModelType'] or 'model',
                "fundCode": row['FundCode'],
                "fundType": row['FundType'],
                "assetClass": row['AssetClass']
            })

        return models

    except Exception as e:
        print(f"Error getting models: {e}")
        return {"error": str(e)}

    finally:
        conn.close()

@app.route('/')
def index():
    """Serve the main page"""
    return send_from_directory('.', 'sqlite_viewer.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('.', filename)

@app.route('/api/models', methods=['GET'])
def api_get_models():
    """API endpoint to get all models"""
    models = get_models()
    if isinstance(models, dict) and "error" in models:
        return jsonify(models), 500
    return jsonify(models)

@app.route('/api/models', methods=['POST'])
def api_add_model():
    """API endpoint to add a new model or mutual fund"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500

        cursor = conn.cursor()

        # Determine if this is a mutual fund or investment model
        is_mutual_fund = 'fundCode' in data and data.get('fundCode')

        if is_mutual_fund:
            # This is a mutual fund
            model_type = 'mutual_fund'
            fund_type = data.get('fundType', '')
            english_category = f"Mutual Funds - {fund_type}" if fund_type else "Mutual Funds - General"
            french_category = f"Fonds Mutuels - {fund_type}" if fund_type else "Fonds Mutuels - Général"

            cursor.execute("""
                INSERT INTO tblModels (
                    EnglishDescription, FrenchDescription, EnglishCategory, FrenchCategory,
                    StartDate, EndDate, IsActive, FixedIncomePercentage, EquityPercentage,
                    AlternativesPercentage, ProgramType, MinimumInvestment,
                    FundCode, FundType, AssetClass, ModelType
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data.get('englishName', data.get('englishDescription')),
                data.get('frenchName', data.get('frenchDescription')),
                english_category,
                french_category,
                data['startDate'],
                data.get('endDate'),
                data.get('isActive', True),
                0,  # Mutual funds don't have allocation percentages
                0,
                0,
                'UMA',  # Default program type for mutual funds
                data.get('minimumInvestment', 0),
                data['fundCode'],
                data.get('fundType'),
                data.get('assetClass'),
                model_type
            ))
        else:
            # This is a regular investment model
            model_type = 'model'

            cursor.execute("""
                INSERT INTO tblModels (
                    EnglishDescription, FrenchDescription, EnglishCategory, FrenchCategory,
                    StartDate, EndDate, IsActive, FixedIncomePercentage, EquityPercentage,
                    AlternativesPercentage, ProgramType, MinimumInvestment, ModelType
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['englishDescription'],
                data['frenchDescription'],
                data['englishCategory'],
                data['frenchCategory'],
                data['startDate'],
                data.get('endDate'),
                data.get('isActive', True),
                data.get('fixedIncome', 0),
                data.get('equity', 0),
                data.get('alternatives', 0),
                data.get('programType', 'UMA'),
                data.get('minimumInvestment', 0),
                model_type
            ))

        conn.commit()
        model_id = cursor.lastrowid

        conn.close()

        return jsonify({"success": True, "modelId": model_id, "type": model_type})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Mutual funds are now handled by the unified /api/models endpoint

@app.route('/api/models/<model_id>', methods=['PUT'])
def api_update_model(model_id):
    """API endpoint to update a model"""
    try:
        data = request.get_json()

        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500

        cursor = conn.cursor()

        # Check if this is a mutual fund (starts with MF_)
        if model_id.startswith('MF_'):
            fund_id = model_id[3:]  # Remove MF_ prefix
            cursor.execute("""
                UPDATE tblMutualFunds SET
                    FundCode = ?, EnglishName = ?, FrenchName = ?, FundType = ?,
                    AssetClass = ?, StartDate = ?, EndDate = ?, IsActive = ?
                WHERE FundID = ?
            """, (
                data.get('fundCode'),
                data['englishDescription'],
                data['frenchDescription'],
                data.get('fundType'),
                data.get('assetClass'),
                data['startDate'],
                data.get('endDate'),
                data.get('isActive', True),
                fund_id
            ))
        else:
            # Update regular model
            cursor.execute("""
                UPDATE tblModels SET
                    EnglishDescription = ?, FrenchDescription = ?, EnglishCategory = ?,
                    FrenchCategory = ?, StartDate = ?, EndDate = ?, IsActive = ?,
                    FixedIncomePercentage = ?, EquityPercentage = ?, AlternativesPercentage = ?,
                    ProgramType = ?, MinimumInvestment = ?
                WHERE ModelID = ?
            """, (
                data['englishDescription'],
                data['frenchDescription'],
                data['englishCategory'],
                data['frenchCategory'],
                data['startDate'],
                data.get('endDate'),
                data.get('isActive', True),
                data.get('fixedIncome', 0),
                data.get('equity', 0),
                data.get('alternatives', 0),
                data.get('programType', 'UMA'),
                data.get('minimumInvestment', 0),
                model_id
            ))

        conn.commit()
        conn.close()

        return jsonify({"success": True})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/models/<model_id>', methods=['DELETE'])
def api_delete_model(model_id):
    """API endpoint to inactivate a model or mutual fund"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500

        cursor = conn.cursor()

        # Check if this is a mutual fund (starts with MF_)
        if model_id.startswith('MF_'):
            fund_id = model_id[3:]  # Remove MF_ prefix
            cursor.execute("UPDATE tblMutualFunds SET IsActive = 0 WHERE FundID = ?", (fund_id,))
        else:
            # Inactivate regular model
            cursor.execute("UPDATE tblModels SET IsActive = 0 WHERE ModelID = ?", (model_id,))

        conn.commit()
        conn.close()

        return jsonify({"success": True})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/fee-programs', methods=['GET'])
def api_get_fee_programs():
    """API endpoint to get fee programs"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM tblFeePrograms ORDER BY EnglishName")

        programs = []
        for row in cursor.fetchall():
            programs.append({
                "programId": row['ProgramID'],
                "englishName": row['EnglishName'],
                "frenchName": row['FrenchName'],
                "percentage": row['Percentage']
            })

        conn.close()
        return jsonify(programs)

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/fee-tiers', methods=['GET'])
def api_get_fee_tiers():
    """API endpoint to get fee tiers"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM tblFeeTiers ORDER BY ProgramID, StartAmount")

        tiers = []
        for row in cursor.fetchall():
            tiers.append({
                "tierId": row['TierID'],
                "programId": row['ProgramID'],
                "startAmount": row['StartAmount'],
                "endAmount": row['EndAmount'],
                "adminCost": row['AdminCost'],
                "advisorFee": row['AdvisorFee']
            })

        conn.close()
        return jsonify(tiers)

    except Exception as e:
        return jsonify({"error": str(e)}), 500

def generate_ips_pdf(ips_data):
    """Generate IPS PDF document"""
    buffer = io.BytesIO()

    # Create the PDF document
    doc = SimpleDocTemplate(buffer, pagesize=letter,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=18)

    # Get styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
        alignment=1,  # Center alignment
        textColor=colors.darkblue
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor=colors.darkblue
    )

    # Build the document content
    story = []

    # Title
    story.append(Paragraph("INVESTMENT POLICY STATEMENT", title_style))
    story.append(Paragraph("Elite Unified Managed Account Program", styles['Normal']))
    story.append(Spacer(1, 20))

    # Client Information Section
    story.append(Paragraph("CLIENT INFORMATION", heading_style))

    client_data = [
        ['Client Name:', ips_data.get('clientName', 'N/A')],
        ['Account Number:', ips_data.get('accountNumber', 'N/A')],
        ['Representative Code:', ips_data.get('repCode', 'N/A')],
        ['Advisor Name:', ips_data.get('advisorName', 'N/A')],
        ['Account Type:', ips_data.get('accountType', 'N/A')],
        ['Program Type:', ips_data.get('programType', 'N/A')],
        ['Document Date:', datetime.now().strftime('%B %d, %Y')]
    ]

    client_table = Table(client_data, colWidths=[2*inch, 4*inch])
    client_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))

    story.append(client_table)
    story.append(Spacer(1, 20))

    # Risk Assessment Section
    story.append(Paragraph("RISK ASSESSMENT", heading_style))

    risk_data = [
        ['Risk Capacity Level:', ips_data.get('riskCapacityLevel', 'N/A')],
        ['Risk Tolerance Level:', ips_data.get('riskToleranceLevel', 'N/A')],
        ['Assessment Method:', ips_data.get('ipqMethod', 'N/A')]
    ]

    if ips_data.get('riskProfileOverride'):
        risk_data.append(['Risk Profile Override:', 'Yes'])
        risk_data.append(['Override Reason:', ips_data.get('overrideReason', 'N/A')])

    risk_table = Table(risk_data, colWidths=[2*inch, 4*inch])
    risk_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))

    story.append(risk_table)
    story.append(Spacer(1, 20))

    # Selected Models Section (if UMA program)
    if ips_data.get('programType') == 'UMA' and ips_data.get('selectedModels'):
        story.append(Paragraph("SELECTED UMA MODELS", heading_style))

        model_headers = ['Model Name', 'Allocation %', 'Fixed Income %', 'Equity %', 'Alternatives %']
        model_data = [model_headers]

        for model_selection in ips_data['selectedModels']:
            model = model_selection.get('model', {})
            model_data.append([
                model.get('englishDescription', 'N/A'),
                f"{model_selection.get('allocation', 0)}%",
                f"{model.get('fixedIncome', 0)}%",
                f"{model.get('equity', 0)}%",
                f"{model.get('alternatives', 0)}%"
            ])

        models_table = Table(model_data, colWidths=[2.5*inch, 1*inch, 1*inch, 1*inch, 1*inch])
        models_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(models_table)
        story.append(Spacer(1, 20))

    # Investment Objectives Section
    story.append(Paragraph("INVESTMENT OBJECTIVES", heading_style))
    objectives_text = """
    The primary objective of this Investment Policy Statement is to establish clear guidelines
    for the management of the client's investment portfolio within the Elite Unified Managed
    Account Program. The investment strategy is designed to align with the client's risk
    profile, investment timeline, and financial objectives as determined through the
    comprehensive risk assessment process.
    """
    story.append(Paragraph(objectives_text, styles['Normal']))
    story.append(Spacer(1, 15))

    # Compliance Section
    story.append(Paragraph("COMPLIANCE AND AUTHORIZATION", heading_style))
    compliance_text = """
    This Investment Policy Statement has been prepared in accordance with applicable
    regulatory requirements and industry best practices. The client acknowledges that
    they have reviewed and understand the investment strategy, risk factors, and
    fee structure associated with their selected program.
    """
    story.append(Paragraph(compliance_text, styles['Normal']))
    story.append(Spacer(1, 30))

    # Signature Section
    signature_data = [
        ['Client Signature:', '_' * 30, 'Date:', '_' * 15],
        ['', '', '', ''],
        ['Advisor Signature:', '_' * 30, 'Date:', '_' * 15],
    ]

    signature_table = Table(signature_data, colWidths=[1.5*inch, 2.5*inch, 0.8*inch, 1.2*inch])
    signature_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))

    story.append(signature_table)

    # Build the PDF
    doc.build(story)
    buffer.seek(0)
    return buffer

@app.route('/api/ips-documents', methods=['POST'])
def api_create_ips_document():
    """API endpoint to create and generate IPS document"""
    try:
        data = request.get_json()

        # Check if this is a draft save or PDF generation
        generate_pdf = request.args.get('generate_pdf', 'true').lower() == 'true'

        # Save to database
        conn = get_db_connection()
        ips_id = None

        if conn:
            try:
                cursor = conn.cursor()

                # Create IPS documents table if it doesn't exist
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS tblIPSDocuments (
                        IPSID INTEGER PRIMARY KEY AUTOINCREMENT,
                        ClientName TEXT,
                        AccountNumber TEXT,
                        RepCode TEXT,
                        AdvisorName TEXT,
                        ProgramType TEXT,
                        AccountType TEXT,
                        RiskCapacityLevel TEXT,
                        RiskToleranceLevel TEXT,
                        IPQMethod TEXT,
                        Status TEXT DEFAULT 'Generated',
                        CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                        DocumentData TEXT
                    )
                """)

                # Insert IPS record
                cursor.execute("""
                    INSERT INTO tblIPSDocuments (
                        ClientName, AccountNumber, RepCode, AdvisorName, ProgramType,
                        AccountType, RiskCapacityLevel, RiskToleranceLevel, IPQMethod,
                        Status, DocumentData
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data.get('clientName'),
                    data.get('accountNumber'),
                    data.get('repCode', 'N/A'),  # Provide default value if missing
                    data.get('advisorName'),
                    data.get('programType'),
                    data.get('accountType'),
                    data.get('riskCapacityLevel'),
                    data.get('riskToleranceLevel'),
                    data.get('ipqMethod'),
                    data.get('status', 'Generated'),
                    json.dumps(data)  # Store full data as JSON
                ))

                conn.commit()
                ips_id = cursor.lastrowid
                conn.close()

            except Exception as db_error:
                print(f"Database error: {db_error}")
                ips_id = f"TEMP_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        else:
            ips_id = f"TEMP_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # If this is a draft save, return JSON response
        if not generate_pdf:
            return jsonify({
                "success": True,
                "ipsId": ips_id,
                "message": "IPS document saved successfully"
            })

        # Generate and return PDF
        pdf_buffer = generate_ips_pdf(data)

        # Create filename
        client_name = data.get('clientName', 'Client').replace(' ', '_')
        filename = f"IPS_{client_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # Return the PDF file
        return send_file(
            pdf_buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )

    except Exception as e:
        print(f"Error generating IPS: {e}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting IPP-UMA System Server...")
    print(f"📊 Database: {DB_PATH}")
    print("🌐 Server will be available at: http://localhost:8000")
    print("📄 Main interface: http://localhost:8000")
    print("➕ Add models: http://localhost:8000/add_model.html")
    print("💰 Fee calculator: http://localhost:8000/fee_calculator.html")
    print("📋 IPS Generator: http://localhost:8000/ips_generator.html")
    print()
    
    app.run(host='0.0.0.0', port=8000, debug=True)
