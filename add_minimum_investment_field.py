import sqlite3

# Add MinimumInvestment field to tblModels table
conn = sqlite3.connect('ipp_uma_models.db')
cursor = conn.cursor()

try:
    # Check if MinimumInvestment column already exists
    cursor.execute("PRAGMA table_info(tblModels)")
    columns = [column[1] for column in cursor.fetchall()]
    
    if 'MinimumInvestment' not in columns:
        # Add the MinimumInvestment column
        cursor.execute('ALTER TABLE tblModels ADD COLUMN MinimumInvestment REAL DEFAULT 0')
        print('MinimumInvestment column added successfully!')
        
        # Update existing models with some default minimum investment amounts
        cursor.execute('''
            UPDATE tblModels 
            SET MinimumInvestment = 
                CASE 
                    WHEN ProgramType = 'Elite UMA' THEN 250000
                    WHEN ProgramType = 'Elite Partner Portfolios' THEN 100000
                    WHEN ProgramType = 'iAPW Strategic Portfolios' THEN 50000
                    ELSE 25000
                END
        ''')
        
        conn.commit()
        print('Updated existing models with default minimum investment amounts')
    else:
        print('MinimumInvestment column already exists!')

    # Verify the update
    cursor.execute('SELECT ModelID, EnglishDescription, MinimumInvestment FROM tblModels LIMIT 5')
    results = cursor.fetchall()
    print('\nSample models with minimum investment:')
    for row in results:
        print(f'ID: {row[0]}, Model: {row[1]}, Min Investment: ${row[2]:,.0f}')

except Exception as e:
    print(f'Error: {e}')
    conn.rollback()

finally:
    conn.close()
