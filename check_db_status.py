#!/usr/bin/env python3
import sqlite3

def check_database():
    conn = sqlite3.connect('ipp_uma_models.db')
    cursor = conn.cursor()
    
    print("🔍 Checking database status...")
    
    # Check table structure
    cursor.execute("PRAGMA table_info(tblModels)")
    columns = [column[1] for column in cursor.fetchall()]
    print(f"📋 Columns in tblModels: {columns}")
    
    # Check total records
    cursor.execute("SELECT COUNT(*) FROM tblModels")
    total = cursor.fetchone()[0]
    print(f"📊 Total records: {total}")
    
    # Check by ModelType
    cursor.execute("SELECT ModelType, COUNT(*) FROM tblModels GROUP BY ModelType")
    types = cursor.fetchall()
    print("📈 Records by type:")
    for model_type, count in types:
        print(f"   {model_type or 'NULL'}: {count}")
    
    # Show sample records
    cursor.execute("SELECT ModelID, EnglishDescription, ModelType, FundCode FROM tblModels LIMIT 10")
    records = cursor.fetchall()
    print("\n📋 Sample records:")
    for record in records:
        print(f"   ID {record[0]}: {record[1]} - Type: {record[2]} - Fund: {record[3]}")
    
    conn.close()

if __name__ == '__main__':
    check_database()
