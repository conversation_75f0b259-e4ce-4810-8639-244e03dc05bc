<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Portfolio Model - IPP-UMA System</title>



    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Main content with sidebar */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top bar */
        .top-bar {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px 35px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-bar h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .top-bar .language-toggle button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .top-bar .language-toggle button:hover {
            background: #3b82f6;
        }

        /* Language display - handled by sidebar.css */

        /* Page-specific styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .content-area button, .form-group button, .top-bar button:not(.lang-btn):not(.quick-action-btn) {
            background-color: #dc3545;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .content-area button:hover, .form-group button:hover, .top-bar button:not(.lang-btn):not(.quick-action-btn):hover {
            background-color: #c82333;
            transform: translateY(-1px);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>

    <!-- Import sidebar CSS AFTER embedded styles to ensure proper precedence -->
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="add_model.html" class="nav-item create-action active" id="addModelNav">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="window.location.href='add_model.html'" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Create Portfolio Model</span>
                <span class="french-text">Créer un Modèle de Portefeuille</span>
            </h1>
            <div class="language-toggle">
                <button id="topEnglishBtn" class="active">English</button>
                <button id="topFrenchBtn">Français</button>
            </div>
        </div>

        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">
                    <span class="english-text">Add New Model</span>
                    <span class="french-text">Ajouter un Nouveau Modèle</span>
                </h1>
                <p class="page-subtitle">
                    <span class="english-text">Create a new UMA investment model or mutual fund</span>
                    <span class="french-text">Créez un nouveau modèle d'investissement UMA ou fonds mutuel</span>
                </p>
            </div>

            <!-- Type Selection Toggle -->
            <div class="form-group" style="margin-bottom: 30px; text-align: center;">
                <div style="display: inline-flex; background: #f5f5f5; border-radius: 25px; padding: 5px; margin-bottom: 20px;">
                    <button type="button" id="modelTypeBtn" class="type-toggle-btn active" onclick="switchToModel()">
                        <span class="english-text">📊 Investment Model</span>
                        <span class="french-text">📊 Modèle d'Investissement</span>
                    </button>
                    <button type="button" id="fundTypeBtn" class="type-toggle-btn" onclick="switchToFund()">
                        <span class="english-text">🏦 Mutual Fund</span>
                        <span class="french-text">🏦 Fonds Mutuel</span>
                    </button>
                </div>
            </div>

            <form id="addModelForm">
        <!-- Common Fields -->
        <div class="form-group">
            <label for="englishDescription">
                <span class="english-text model-field">English Description:</span>
                <span class="french-text model-field">Description Anglaise:</span>
                <span class="english-text fund-field" style="display: none;">English Fund Name:</span>
                <span class="french-text fund-field" style="display: none;">Nom du Fonds Anglais:</span>
            </label>
            <input type="text" id="englishDescription" required>
        </div>

        <div class="form-group">
            <label for="frenchDescription">
                <span class="english-text model-field">French Description:</span>
                <span class="french-text model-field">Description Française:</span>
                <span class="english-text fund-field" style="display: none;">French Fund Name:</span>
                <span class="french-text fund-field" style="display: none;">Nom du Fonds Français:</span>
            </label>
            <input type="text" id="frenchDescription" required>
        </div>

        <!-- Mutual Fund Specific Fields -->
        <div class="form-group fund-field" style="display: none;">
            <label for="fundCode">
                <span class="english-text">Fund Code (Ticker):</span>
                <span class="french-text">Code du Fonds (Symbole):</span>
            </label>
            <input type="text" id="fundCode" placeholder="e.g., FUND001">
        </div>

        <div class="form-group fund-field" style="display: none;">
            <label for="fundType">
                <span class="english-text">Fund Type:</span>
                <span class="french-text">Type de Fonds:</span>
            </label>
            <select id="fundType">
                <option value="">-- Select Fund Type --</option>
                <option value="Equity">Equity / Actions</option>
                <option value="Fixed Income">Fixed Income / Revenu Fixe</option>
                <option value="Balanced">Balanced / Équilibré</option>
                <option value="Money Market">Money Market / Marché Monétaire</option>
                <option value="Alternative">Alternative / Alternatif</option>
                <option value="Sector">Sector / Sectoriel</option>
                <option value="International">International / International</option>
            </select>
        </div>

        <div class="form-group fund-field" style="display: none;">
            <label for="assetClass">
                <span class="english-text">Asset Class:</span>
                <span class="french-text">Classe d'Actifs:</span>
            </label>
            <select id="assetClass">
                <option value="">-- Select Asset Class --</option>
                <option value="Canadian Equity">Canadian Equity / Actions Canadiennes</option>
                <option value="US Equity">US Equity / Actions Américaines</option>
                <option value="International Equity">International Equity / Actions Internationales</option>
                <option value="Bonds">Bonds / Obligations</option>
                <option value="Cash">Cash / Liquidités</option>
                <option value="Real Estate">Real Estate / Immobilier</option>
                <option value="Commodities">Commodities / Matières Premières</option>
                <option value="Mixed">Mixed / Mixte</option>
            </select>
        </div>

        <!-- Model Specific Fields -->
        <div class="form-group model-field">
            <label for="englishCategory">English Category:</label>
            <input type="text" id="englishCategory">
        </div>

        <div class="form-group model-field">
            <label for="frenchCategory">French Category:</label>
            <input type="text" id="frenchCategory">
        </div>

        <div class="form-group">
            <label for="startDate">Start Date:</label>
            <input type="date" id="startDate" required>
        </div>

        <!-- Asset Allocation Fields (Model Only) -->
        <div class="form-group model-field">
            <label for="fixedIncome">Fixed Income Percentage:</label>
            <input type="number" id="fixedIncome" min="0" max="100" value="0">
        </div>

        <div class="form-group model-field">
            <label for="equity">Equity Percentage:</label>
            <input type="number" id="equity" min="0" max="100" value="0">
        </div>

        <div class="form-group model-field">
            <label for="alternatives">Alternatives Percentage:</label>
            <input type="number" id="alternatives" min="0" max="100" value="0">
        </div>

        <div class="form-group model-field">
            <label for="programType">Program Type:</label>
            <select id="programType">
                <option value="">-- Select Program Type --</option>
                <option value="Elite MF Partner Program Fee Schedule">Elite MF Partner Program Fee Schedule</option>
                <option value="Elite SMA Fee Schedule - 3rd party equity">Elite SMA Fee Schedule - 3rd party equity</option>
                <option value="Elite SMA Fee Schedule - Laddered bond">Elite SMA Fee Schedule - Laddered bond</option>
                <option value="Elite SMA Fee Schedule - IOOAM Models">Elite SMA Fee Schedule - IOOAM Models</option>
                <option value="Elite a la carte MF Fee Schedule">Elite a la carte MF Fee Schedule</option>
                <option value="Elite SMA Fee Schedule - 3rd party FI">Elite SMA Fee Schedule - 3rd party FI</option>
            </select>
        </div>

        <div class="form-group model-field">
            <label>Total: <span id="total">0</span>%</label>
            <div id="totalWarning" style="color: red; display: none;">Total must be 100%</div>
        </div>

        <!-- Active Status -->
        <div class="form-group">
            <label>
                <input type="checkbox" id="isActive" checked>
                <span class="english-text">Active</span>
                <span class="french-text">Actif</span>
            </label>
        </div>

        <button type="submit" id="submitBtn">
            <span class="english-text model-field">Add Model</span>
            <span class="french-text model-field">Ajouter le Modèle</span>
            <span class="english-text fund-field" style="display: none;">Add Mutual Fund</span>
            <span class="french-text fund-field" style="display: none;">Ajouter le Fonds Mutuel</span>
        </button>
    </form>

    <div id="result" class="result" style="display: none;"></div>

    <style>
        .type-toggle-btn {
            background: white;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            margin: 0 2px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .type-toggle-btn.active {
            background: #007bff;
            color: white;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        .type-toggle-btn:hover:not(.active) {
            background: #e9ecef;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        button[type="submit"] {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background 0.3s ease;
        }

        button[type="submit"]:hover {
            background: #0056b3;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: 500;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>

    <script>
        let currentFormType = 'model'; // 'model' or 'fund'

        // Form type switching functions
        function switchToModel() {
            currentFormType = 'model';
            document.getElementById('modelTypeBtn').classList.add('active');
            document.getElementById('fundTypeBtn').classList.remove('active');

            // Show model fields, hide fund fields
            document.querySelectorAll('.model-field').forEach(el => el.style.display = 'block');
            document.querySelectorAll('.fund-field').forEach(el => el.style.display = 'none');

            // Update required fields
            document.getElementById('englishCategory').required = true;
            document.getElementById('frenchCategory').required = true;
            document.getElementById('fixedIncome').required = true;
            document.getElementById('equity').required = true;
            document.getElementById('alternatives').required = true;
            document.getElementById('programType').required = true;

            document.getElementById('fundCode').required = false;
            document.getElementById('fundType').required = false;
            document.getElementById('assetClass').required = false;
        }

        function switchToFund() {
            currentFormType = 'fund';
            document.getElementById('fundTypeBtn').classList.add('active');
            document.getElementById('modelTypeBtn').classList.remove('active');

            // Show fund fields, hide model fields
            document.querySelectorAll('.fund-field').forEach(el => el.style.display = 'block');
            document.querySelectorAll('.model-field').forEach(el => el.style.display = 'none');

            // Update required fields
            document.getElementById('fundCode').required = true;
            document.getElementById('fundType').required = true;
            document.getElementById('assetClass').required = true;

            document.getElementById('englishCategory').required = false;
            document.getElementById('frenchCategory').required = false;
            document.getElementById('fixedIncome').required = false;
            document.getElementById('equity').required = false;
            document.getElementById('alternatives').required = false;
            document.getElementById('programType').required = false;
        }

        // Update total percentage (for models only)
        const fixedIncomeInput = document.getElementById('fixedIncome');
        const equityInput = document.getElementById('equity');
        const alternativesInput = document.getElementById('alternatives');
        const totalSpan = document.getElementById('total');
        const totalWarning = document.getElementById('totalWarning');

        function updateTotal() {
            if (currentFormType !== 'model') return;

            const fixedIncome = parseInt(fixedIncomeInput.value) || 0;
            const equity = parseInt(equityInput.value) || 0;
            const alternatives = parseInt(alternativesInput.value) || 0;

            const total = fixedIncome + equity + alternatives;
            totalSpan.textContent = total;

            if (total !== 100) {
                totalWarning.style.display = 'block';
            } else {
                totalWarning.style.display = 'none';
            }
        }

        fixedIncomeInput.addEventListener('input', updateTotal);
        equityInput.addEventListener('input', updateTotal);
        alternativesInput.addEventListener('input', updateTotal);

        // Form submission
        document.getElementById('addModelForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            let data, endpoint, successMessage;

            if (currentFormType === 'model') {
                // Check if total is 100% for models
                const fixedIncome = parseInt(fixedIncomeInput.value) || 0;
                const equity = parseInt(equityInput.value) || 0;
                const alternatives = parseInt(alternativesInput.value) || 0;

                if (fixedIncome + equity + alternatives !== 100) {
                    alert('Total percentage must be 100%');
                    return;
                }

                // Prepare model data
                data = {
                    englishDescription: document.getElementById('englishDescription').value,
                    frenchDescription: document.getElementById('frenchDescription').value,
                    englishCategory: document.getElementById('englishCategory').value,
                    frenchCategory: document.getElementById('frenchCategory').value,
                    startDate: document.getElementById('startDate').value,
                    isActive: document.getElementById('isActive').checked,
                    fixedIncome: fixedIncome,
                    equity: equity,
                    alternatives: alternatives,
                    programType: document.getElementById('programType').value
                };
                successMessage = 'Model added successfully!';
            } else {
                // Prepare mutual fund data (using same endpoint but with fund-specific fields)
                data = {
                    fundCode: document.getElementById('fundCode').value,
                    englishName: document.getElementById('englishDescription').value,
                    frenchName: document.getElementById('frenchDescription').value,
                    fundType: document.getElementById('fundType').value,
                    assetClass: document.getElementById('assetClass').value,
                    startDate: document.getElementById('startDate').value,
                    isActive: document.getElementById('isActive').checked
                };
                successMessage = 'Mutual fund added successfully!';
            }

            // Both types use the same endpoint now
            endpoint = '/api/models';

            try {
                // Send POST request
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                // Display result
                const resultDiv = document.getElementById('result');

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = successMessage;

                    // Reset form
                    this.reset();
                    if (currentFormType === 'model') {
                        updateTotal();
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = result.error || `Failed to add ${currentFormType}`;
                }

                resultDiv.style.display = 'block';

                // Hide result after 5 seconds
                setTimeout(() => {
                    resultDiv.style.display = 'none';
                }, 5000);

            } catch (error) {
                console.error('Error:', error);

                // Display error
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result error';
                resultDiv.textContent = 'An error occurred. Please try again.';
                resultDiv.style.display = 'block';
            }
        });

        // Set today's date as default
        document.getElementById('startDate').valueAsDate = new Date();

        // Language toggle function
        let currentLanguage = 'en';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fr' : 'en';
            updateLanguageDisplay();
        }

        function updateLanguageDisplay() {
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            if (currentLanguage === 'en') {
                englishElements.forEach(el => el.style.display = 'inline');
                frenchElements.forEach(el => el.style.display = 'none');
            } else {
                englishElements.forEach(el => el.style.display = 'none');
                frenchElements.forEach(el => el.style.display = 'inline');
            }
        }

        // Initialize language display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
        });
    </script>
        </div>
    </div>

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the sidebar functionality (HTML is now inline)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar functionality
            if (typeof initializeSidebar === 'function') {
                initializeSidebar();
            }
            if (typeof initializeLanguageSwitching === 'function') {
                initializeLanguageSwitching();
            }
            if (typeof setActiveNavItem === 'function') {
                setActiveNavItem();
            }
            if (typeof updateLanguageDisplay === 'function') {
                updateLanguageDisplay();
            }
        });
    </script>
</body>
</html>
