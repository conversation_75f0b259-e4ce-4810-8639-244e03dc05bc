<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Calculator - IPP-UMA System</title>



    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Main content with sidebar */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top bar */
        .top-bar {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px 35px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-bar h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .top-bar .language-toggle button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .top-bar .language-toggle button:hover {
            background: #3b82f6;
        }

        /* Language display - handled by sidebar.css */

        /* Specific button styles for fee calculator - scoped to avoid sidebar conflicts */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
            transform: translateY(-1px);
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
            transform: translateY(-1px);
        }

        /* Page-specific styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        select, input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 12px 24px;
            font-size: 16px;
            line-height: 1.5;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
        }

        .btn-success {
            color: #fff;
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
            transform: translateY(-1px);
        }

        .btn-primary {
            color: #fff;
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .btn-primary:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-1px);
        }

        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .fee-schedule-table {
            margin-top: 30px;
        }

        .hidden {
            display: none;
        }

        .tab {
            overflow: hidden;
            border: 1px solid #dee2e6;
            background-color: #f8f9fa;
            margin-bottom: 20px;
            border-radius: 6px 6px 0 0;
        }

        .tab .tablinks {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 20px;
            transition: 0.3s;
            color: #495057;
            font-weight: 500;
        }

        .tab .tablinks:hover {
            background-color: #e9ecef;
        }

        .tab .tablinks.active {
            background-color: #007bff;
            color: white;
        }

        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 6px 6px;
            background-color: white;
        }

        /* Page-specific styles */
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #6c757d;
            font-size: 1.1em;
        }

        .nav-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: #0056b3;
        }
    </style>

    <!-- Import sidebar CSS AFTER embedded styles to ensure proper precedence -->
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="add_model.html" class="nav-item create-action" id="addModelNav">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item active" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="window.location.href='add_model.html'" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Fee Calculator</span>
                <span class="french-text">Calculateur de Frais</span>
            </h1>
            <div class="language-toggle">
                <button id="topEnglishBtn" class="active">English</button>
                <button id="topFrenchBtn">Français</button>
            </div>
        </div>

        <div class="container">
            <div class="header">
                <h1>IPP-UMA Fee Calculator</h1>
                <p>Calculate fees for UMA models based on investment amounts</p>
            </div>

        <div class="tab">
            <button class="tablinks active" onclick="openTab(event, 'calculatorTab')" id="calculatorTabBtn">Fee Calculator</button>
            <button class="tablinks" onclick="openTab(event, 'modelsTab')" id="modelsTabBtn">Models</button>
            <button class="tablinks" onclick="location.href='/blended_fee_calculator.html'" id="blendedFeeBtn">Blended Fee Calculator</button>
        </div>

        <div id="calculatorTab" class="tabcontent" style="display: block;">
            <div class="form-group">
                <label for="modelSelect" id="modelSelectLabel">Select Model:</label>
                <select id="modelSelect">
                    <option value="">-- Select a model --</option>
                </select>
            </div>

            <div class="form-group">
                <label for="investmentAmount" id="investmentAmountLabel">Investment Amount ($):</label>
                <input type="number" id="investmentAmount" min="0" step="1000" value="100000">
            </div>

            <button onclick="calculateFees()" id="calculateBtn" class="btn btn-success">Calculate Fees</button>

            <div id="results" class="results hidden">
                <h2 id="resultsTitle">Fee Calculation Results</h2>
                <div id="resultsContent"></div>

                <div class="fee-schedule-table">
                    <h3 id="feeScheduleTitle">Fee Schedule</h3>
                    <table id="feeScheduleTable">
                        <thead>
                            <tr>
                                <th id="tierHeader">Tier</th>
                                <th id="adminCostHeader">Admin Cost (%)</th>
                                <th id="advisorFeeHeader">Advisor Fee (%)</th>
                                <th id="totalFeeHeader">Total Client Fee (%)</th>
                            </tr>
                        </thead>
                        <tbody id="feeScheduleBody"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="modelsTab" class="tabcontent">
            <h2 id="modelsTitle">Available Models</h2>
            <table id="modelsTable">
                <thead>
                    <tr>
                        <th id="modelNameHeader">Model Name</th>
                        <th id="categoryHeader">Category</th>
                        <th id="programTypeHeader">Program Type</th>
                        <th id="assetAllocationHeader">Asset Allocation</th>
                    </tr>
                </thead>
                <tbody id="modelsTableBody"></tbody>
            </table>

            <button onclick="location.href='add_model.html'" class="add-model-btn btn btn-danger" id="addModelBtn">Add New Model</button>
        </div>
        </div> <!-- Close container -->
    </div> <!-- Close main-content -->

    <script>
        let currentLanguage = 'en';
        let models = [];
        let feePrograms = [];
        let feeTiers = [];

        // Load data when page loads
        window.onload = function() {
            loadModels();
            loadFeePrograms();
            loadFeeTiers();
        };

        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;

            // Hide all tab content
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // Remove active class from all tab buttons
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }

            // Show the current tab and add active class to the button
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }

        function loadModels() {
            fetch('/api/models')
                .then(response => response.json())
                .then(data => {
                    models = data;
                    populateModelDropdown();
                    populateModelsTable();
                })
                .catch(error => console.error('Error loading models:', error));
        }

        function loadFeePrograms() {
            fetch('/api/fee-programs')
                .then(response => response.json())
                .then(data => {
                    feePrograms = data;
                })
                .catch(error => console.error('Error loading fee programs:', error));
        }

        function loadFeeTiers() {
            fetch('/api/fee-tiers')
                .then(response => response.json())
                .then(data => {
                    feeTiers = data;
                })
                .catch(error => console.error('Error loading fee tiers:', error));
        }

        function populateModelDropdown() {
            const select = document.getElementById('modelSelect');

            // Clear existing options except the first one
            while (select.options.length > 1) {
                select.remove(1);
            }

            // Add models to dropdown (always use English for simplicity)
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.modelId;
                option.textContent = model.englishDescription;
                select.appendChild(option);
            });
        }

        function populateModelsTable() {
            const tableBody = document.getElementById('modelsTableBody');
            tableBody.innerHTML = '';

            models.forEach(model => {
                const row = document.createElement('tr');

                // Model name (always use English for simplicity)
                const nameCell = document.createElement('td');
                nameCell.textContent = model.englishDescription;
                row.appendChild(nameCell);

                // Category
                const categoryCell = document.createElement('td');
                categoryCell.textContent = model.englishCategory;
                row.appendChild(categoryCell);

                // Program type
                const programTypeCell = document.createElement('td');
                programTypeCell.textContent = model.programType || '';
                row.appendChild(programTypeCell);

                // Asset allocation
                const assetAllocationCell = document.createElement('td');
                assetAllocationCell.textContent = `FI: ${model.fixedIncome}%, EQ: ${model.equity}%, ALT: ${model.alternatives}%`;
                row.appendChild(assetAllocationCell);

                tableBody.appendChild(row);
            });
        }

        function calculateFees() {
            const modelId = document.getElementById('modelSelect').value;
            const investmentAmount = parseFloat(document.getElementById('investmentAmount').value);

            if (!modelId) {
                alert(currentLanguage === 'en' ? 'Please select a model' : 'Veuillez sélectionner un modèle');
                return;
            }

            if (isNaN(investmentAmount) || investmentAmount <= 0) {
                alert(currentLanguage === 'en' ? 'Please enter a valid investment amount' : 'Veuillez entrer un montant d\'investissement valide');
                return;
            }

            // Get the selected model
            const model = models.find(m => m.modelId == modelId);

            if (!model || !model.programType) {
                alert(currentLanguage === 'en' ? 'Selected model does not have a program type' : 'Le modèle sélectionné n\'a pas de type de programme');
                return;
            }

            // Find the fee program for the model
            const feeProgram = feePrograms.find(fp => fp.englishName === model.programType);

            if (!feeProgram) {
                alert(currentLanguage === 'en' ? 'Fee program not found for the selected model' : 'Programme de frais non trouvé pour le modèle sélectionné');
                return;
            }

            // Get the fee tiers for the program
            const programTiers = feeTiers.filter(ft => ft.programId === feeProgram.programId);

            if (programTiers.length === 0) {
                alert(currentLanguage === 'en' ? 'Fee tiers not found for the selected program' : 'Niveaux de frais non trouvés pour le programme sélectionné');
                return;
            }

            // Find the applicable tier for the investment amount
            const applicableTier = programTiers.find(tier =>
                investmentAmount >= tier.startAmount && investmentAmount <= tier.endAmount
            );

            if (!applicableTier) {
                alert(currentLanguage === 'en' ? 'No applicable fee tier found for the investment amount' : 'Aucun niveau de frais applicable trouvé pour le montant d\'investissement');
                return;
            }

            // Calculate fees
            const adminCost = applicableTier.adminCost;
            const advisorFee = applicableTier.advisorFee;
            const totalFee = adminCost + advisorFee;

            // Display results
            const resultsDiv = document.getElementById('results');
            resultsDiv.classList.remove('hidden');

            const resultsContent = document.getElementById('resultsContent');
            resultsContent.innerHTML = `
                <p><strong>${currentLanguage === 'en' ? 'Model' : 'Modèle'}:</strong> ${currentLanguage === 'en' ? model.englishDescription : model.frenchDescription}</p>
                <p><strong>${currentLanguage === 'en' ? 'Program' : 'Programme'}:</strong> ${currentLanguage === 'en' ? feeProgram.englishName : feeProgram.frenchName}</p>
                <p><strong>${currentLanguage === 'en' ? 'Investment Amount' : 'Montant de l\'investissement'}:</strong> $${investmentAmount.toLocaleString()}</p>
                <p><strong>${currentLanguage === 'en' ? 'Admin Cost' : 'Coût administratif'}:</strong> ${adminCost.toFixed(2)}%</p>
                <p><strong>${currentLanguage === 'en' ? 'Advisor Fee' : 'Frais de conseiller'}:</strong> ${advisorFee.toFixed(2)}%</p>
                <p><strong>${currentLanguage === 'en' ? 'Total Client Fee' : 'Frais client total'}:</strong> ${totalFee.toFixed(2)}%</p>
                <p><strong>${currentLanguage === 'en' ? 'Annual Fee Amount' : 'Montant des frais annuels'}:</strong> $${(investmentAmount * totalFee / 100).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</p>
            `;

            // Populate fee schedule table
            const feeScheduleBody = document.getElementById('feeScheduleBody');
            feeScheduleBody.innerHTML = '';

            programTiers.sort((a, b) => a.startAmount - b.startAmount).forEach(tier => {
                const row = document.createElement('tr');

                // Tier
                const tierCell = document.createElement('td');
                tierCell.textContent = `$${tier.startAmount.toLocaleString()} - $${tier.endAmount.toLocaleString()}`;
                row.appendChild(tierCell);

                // Admin cost
                const adminCostCell = document.createElement('td');
                adminCostCell.textContent = `${tier.adminCost.toFixed(2)}%`;
                row.appendChild(adminCostCell);

                // Advisor fee
                const advisorFeeCell = document.createElement('td');
                advisorFeeCell.textContent = `${tier.advisorFee.toFixed(2)}%`;
                row.appendChild(advisorFeeCell);

                // Total fee
                const totalFeeCell = document.createElement('td');
                totalFeeCell.textContent = `${(tier.adminCost + tier.advisorFee).toFixed(2)}%`;
                row.appendChild(totalFeeCell);

                feeScheduleBody.appendChild(row);

                // Highlight the applicable tier
                if (tier.startAmount <= investmentAmount && tier.endAmount >= investmentAmount) {
                    row.style.backgroundColor = '#e6f7e6';
                    row.style.fontWeight = 'bold';
                }
            });
        }

        // Language toggle function
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fr' : 'en';
            updateLanguageDisplay();
        }

        function updateLanguageDisplay() {
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            if (currentLanguage === 'en') {
                englishElements.forEach(el => el.style.display = 'inline');
                frenchElements.forEach(el => el.style.display = 'none');
            } else {
                englishElements.forEach(el => el.style.display = 'none');
                frenchElements.forEach(el => el.style.display = 'inline');
            }

            // Update specific elements that need language-specific content
            updateLanguageSpecificContent();
        }

        function updateLanguageSpecificContent() {
            // Update form labels and buttons
            const elements = {
                'modelSelectLabel': {
                    'en': 'Select Model:',
                    'fr': 'Sélectionner le modèle:'
                },
                'investmentAmountLabel': {
                    'en': 'Investment Amount ($):',
                    'fr': 'Montant de l\'investissement ($):'
                },
                'calculateBtn': {
                    'en': 'Calculate Fees',
                    'fr': 'Calculer les frais'
                },
                'calculatorTabBtn': {
                    'en': 'Fee Calculator',
                    'fr': 'Calculateur de frais'
                },
                'modelsTabBtn': {
                    'en': 'Models',
                    'fr': 'Modèles'
                },
                'blendedFeeBtn': {
                    'en': 'Blended Fee Calculator',
                    'fr': 'Calculateur de frais mixtes'
                },
                'addModelBtn': {
                    'en': 'Add New Model',
                    'fr': 'Ajouter un nouveau modèle'
                },
                'resultsTitle': {
                    'en': 'Fee Calculation Results',
                    'fr': 'Résultats du calcul des frais'
                },
                'feeScheduleTitle': {
                    'en': 'Fee Schedule',
                    'fr': 'Barème des frais'
                },
                'modelsTitle': {
                    'en': 'Available Models',
                    'fr': 'Modèles disponibles'
                },
                'tierHeader': {
                    'en': 'Tier',
                    'fr': 'Niveau'
                },
                'adminCostHeader': {
                    'en': 'Admin Cost (%)',
                    'fr': 'Coût admin (%)'
                },
                'advisorFeeHeader': {
                    'en': 'Advisor Fee (%)',
                    'fr': 'Frais conseiller (%)'
                },
                'totalFeeHeader': {
                    'en': 'Total Client Fee (%)',
                    'fr': 'Frais client total (%)'
                },
                'modelNameHeader': {
                    'en': 'Model Name',
                    'fr': 'Nom du modèle'
                },
                'categoryHeader': {
                    'en': 'Category',
                    'fr': 'Catégorie'
                },
                'programTypeHeader': {
                    'en': 'Program Type',
                    'fr': 'Type de programme'
                },
                'assetAllocationHeader': {
                    'en': 'Asset Allocation',
                    'fr': 'Répartition des actifs'
                }
            };

            Object.keys(elements).forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element && elements[elementId][currentLanguage]) {
                    element.textContent = elements[elementId][currentLanguage];
                }
            });

            // Update dropdown placeholder
            const modelSelect = document.getElementById('modelSelect');
            if (modelSelect && modelSelect.options[0]) {
                modelSelect.options[0].textContent = currentLanguage === 'en' ?
                    '-- Select a model --' : '-- Sélectionner un modèle --';
            }
        }

        // Initialize language display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
        });
    </script>

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the sidebar functionality (HTML is now inline)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar functionality
            if (typeof initializeSidebar === 'function') {
                initializeSidebar();
            }
            if (typeof initializeLanguageSwitching === 'function') {
                initializeLanguageSwitching();
            }
            if (typeof setActiveNavItem === 'function') {
                setActiveNavItem();
            }
            if (typeof updateLanguageDisplay === 'function') {
                updateLanguageDisplay();
            }
        });
    </script>
</body>
</html>
