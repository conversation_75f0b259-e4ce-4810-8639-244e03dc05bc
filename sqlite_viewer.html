<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Models Overview - IPP-UMA System</title>
    <link rel="stylesheet" href="sidebar.css">


    <style>
        /* Card and Table Styles */
        .category {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .category-header {
            font-size: 1.3em;
            font-weight: 600;
            margin: 0;
            padding: 20px;
            color: #2c3e50;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        tbody tr:last-child td {
            border-bottom: none;
        }
        .asset-allocation {
            display: flex;
            margin-top: 5px;
        }
        .asset-bar {
            height: 20px;
            display: inline-block;
            text-align: center;
            color: white;
            font-size: 12px;
            line-height: 20px;
        }
        .fixed-income {
            background-color: #4e73df;
        }
        .equity {
            background-color: #1cc88a;
        }
        .alternatives {
            background-color: #f6c23e;
        }
        /* Search and Utility Styles */
        .search-container {
            margin-bottom: 30px;
        }

        #searchInput {
            padding: 12px 16px;
            width: 100%;
            max-width: 400px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        #searchInput:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1em;
        }

        .error {
            color: #721c24;
            padding: 15px;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            background-color: #f8d7da;
            margin-bottom: 20px;
        }

        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .success-message {
            color: #155724;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            background-color: #d4edda;
        }

        /* Button Styles */
        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 10px 20px;
            font-size: 14px;
            line-height: 1.5;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
        }

        .btn-primary {
            color: #fff;
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .btn-primary:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-1px);
        }

        .btn-success {
            color: #fff;
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
            transform: translateY(-1px);
        }
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border: none;
            width: 90%;
            max-width: 600px;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #2c3e50;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            display: block;
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 6px;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row > div {
            flex: 1;
        }

        .text-danger {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }

        /* Status indicators */
        .status-active {
            background-color: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-inactive {
            background-color: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        /* Inactive model styling */
        .inactive-model {
            opacity: 0.6;
            background-color: #f8f9fa;
        }

        /* Actions cell */
        .actions-cell {
            white-space: nowrap;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
            margin-right: 5px;
        }

        .btn-secondary {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        .btn-danger {
            color: #fff;
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        .btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
        }

        /* Ensure buttons are visible */
        .actions-cell button {
            display: inline-block !important;
            visibility: visible !important;
        }

        /* Make sure table cells don't hide content */
        table td {
            overflow: visible;
        }

        /* Ensure table is wide enough for all columns */
        .category table {
            width: 100%;
            min-width: 1200px;
            table-layout: auto;
        }

        /* Actions column specific styling */
        .actions-cell {
            min-width: 150px;
            width: 150px;
        }

        /* Category input container styling */
        .category-input-container {
            position: relative;
        }

        .category-select, .category-input {
            margin-bottom: 5px;
        }

        .category-input {
            border: 2px dashed #007bff;
            background-color: #f8f9fa;
        }

        .category-input:focus {
            border-color: #0056b3;
            background-color: #ffffff;
        }

        /* Page-specific responsive adjustments */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .modal-content {
                margin: 10% auto;
                padding: 20px;
            }

            table {
                font-size: 14px;
            }

            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item primary" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="#" class="nav-item create-action" id="addModelNav" onclick="openAddModelModal()">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="openAddModelModal()" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Portfolio Models Overview</span>
                <span class="french-text" style="display: none;">Aperçu des Modèles de Portefeuille</span>
            </h1>
            <div class="language-toggle">
                <button id="englishBtn" class="active">English</button>
                <button id="frenchBtn">Français</button>
            </div>
        </div>

        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">
                    <span class="english-text">UMA Models Viewer</span>
                    <span class="french-text" style="display: none;">Visualiseur de Modèles UMA</span>
                </h1>
                <p class="page-subtitle">
                    <span class="english-text">View and manage your investment models with real-time database connection</span>
                    <span class="french-text" style="display: none;">Visualisez et gérez vos modèles d'investissement avec une connexion de base de données en temps réel</span>
                </p>
            </div>

            <div class="info">
                <strong>
                    <span class="english-text">Connected to database:</span>
                    <span class="french-text" style="display: none;">Connecté à la base de données:</span>
                </strong>
                <span class="english-text">This viewer is connected to your SQLite database.</span>
                <span class="french-text" style="display: none;">Ce visualiseur est connecté à votre base de données SQLite.</span>
            </div>

            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search models...">
            </div>

            <div id="errorContainer" style="display: none;" class="error"></div>
            <div id="successContainer" style="display: none;" class="success-message"></div>

            <div id="modelsContainer">
                <div class="loading">
                    <span class="english-text">Loading models data...</span>
                    <span class="french-text" style="display: none;">Chargement des données des modèles...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Model Modal -->
    <div id="addModelModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>
                <span class="english-text">Add New Model</span>
                <span class="french-text" style="display: none;">Ajouter un Nouveau Modèle</span>
            </h2>

            <form id="addModelForm">
                <div class="form-group">
                    <label for="englishDescription">
                        <span class="english-text">English Description</span>
                        <span class="french-text" style="display: none;">Description en Anglais</span>
                    </label>
                    <input type="text" id="englishDescription" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="frenchDescription">
                        <span class="english-text">French Description</span>
                        <span class="french-text" style="display: none;">Description en Français</span>
                    </label>
                    <input type="text" id="frenchDescription" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="englishCategory">
                        <span class="english-text">English Category</span>
                        <span class="french-text" style="display: none;">Catégorie en Anglais</span>
                    </label>
                    <div class="category-input-container">
                        <select id="englishCategorySelect" class="form-control category-select">
                            <option value="">-- Select existing category --</option>
                            <option value="__new__">+ Create new category</option>
                        </select>
                        <input type="text" id="englishCategory" class="form-control category-input" placeholder="Enter new category name" style="display: none;" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="frenchCategory">
                        <span class="english-text">French Category</span>
                        <span class="french-text" style="display: none;">Catégorie en Français</span>
                    </label>
                    <div class="category-input-container">
                        <select id="frenchCategorySelect" class="form-control category-select">
                            <option value="">-- Sélectionner une catégorie existante --</option>
                            <option value="__new__">+ Créer une nouvelle catégorie</option>
                        </select>
                        <input type="text" id="frenchCategory" class="form-control category-input" placeholder="Entrez le nom de la nouvelle catégorie" style="display: none;" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="startDate">
                        <span class="english-text">Start Date</span>
                        <span class="french-text" style="display: none;">Date de Début</span>
                    </label>
                    <input type="date" id="startDate" class="form-control" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="endDate">
                            <span class="english-text">End Date (Optional)</span>
                            <span class="french-text" style="display: none;">Date de Fin (Optionnel)</span>
                        </label>
                        <input type="date" id="endDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="minimumInvestment">
                            <span class="english-text">Minimum Investment ($)</span>
                            <span class="french-text" style="display: none;">Investissement Minimum ($)</span>
                        </label>
                        <input type="number" id="minimumInvestment" class="form-control" min="0" step="1000" value="0">
                    </div>
                </div>



                <div class="form-group" style="text-align: right;">
                    <button type="button" id="cancelBtn" class="btn">
                        <span class="english-text">Cancel</span>
                        <span class="french-text" style="display: none;">Annuler</span>
                    </button>
                    <button type="submit" id="saveModelBtn" class="btn btn-success">
                        <span class="english-text">Save Model</span>
                        <span class="french-text" style="display: none;">Enregistrer le Modèle</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Current language (default: English)
        let currentLanguage = 'english';
        let allModels = [];

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            fetchModels();
            updateLanguageDisplay();
        });

        // Function to fetch models from the API
        async function fetchModels() {
            try {
                const response = await fetch('/api/models');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                allModels = data;
                displayModels(currentLanguage);
                populateCategoryDropdowns();
            } catch (error) {
                console.error('Error fetching models:', error);
                document.getElementById('errorContainer').textContent =
                    `Error loading models: ${error.message}`;
                document.getElementById('errorContainer').style.display = 'block';
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // Function to populate category dropdowns with existing categories
        function populateCategoryDropdowns() {
            const englishCategories = new Set();
            const frenchCategories = new Set();

            // Extract unique categories from existing models
            allModels.forEach(model => {
                if (model.englishCategory) {
                    englishCategories.add(model.englishCategory);
                }
                if (model.frenchCategory) {
                    frenchCategories.add(model.frenchCategory);
                }
            });

            // Populate English category dropdown
            const englishSelect = document.getElementById('englishCategorySelect');
            // Clear existing options except the first two
            while (englishSelect.options.length > 2) {
                englishSelect.remove(2);
            }

            Array.from(englishCategories).sort().forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                englishSelect.appendChild(option);
            });

            // Populate French category dropdown
            const frenchSelect = document.getElementById('frenchCategorySelect');
            // Clear existing options except the first two
            while (frenchSelect.options.length > 2) {
                frenchSelect.remove(2);
            }

            Array.from(frenchCategories).sort().forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                frenchSelect.appendChild(option);
            });
        }

        // Function to display models based on the selected language
        function displayModels(language) {
            const modelsContainer = document.getElementById('modelsContainer');
            modelsContainer.innerHTML = '';

            if (allModels.length === 0) {
                modelsContainer.innerHTML = '<div class="loading">No models found in the database.</div>';
                return;
            }

            // Group models by category
            const categories = {};
            allModels.forEach(model => {
                const categoryName = language === 'english' ? model.englishCategory : model.frenchCategory;
                if (!categories[categoryName]) {
                    categories[categoryName] = [];
                }
                categories[categoryName].push(model);
            });

            // Display each category and its models
            Object.keys(categories).sort().forEach(categoryName => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'category';

                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'category-header';
                categoryHeader.textContent = categoryName;
                categoryDiv.appendChild(categoryHeader);

                const table = document.createElement('table');
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                // Table headers
                const headers = language === 'english'
                    ? ['Model', 'Start Date', 'End Date', 'Min. Investment', 'Status', 'Actions']
                    : ['Modèle', 'Date Début', 'Date Fin', 'Invest. Min.', 'Statut', 'Actions'];

                headers.forEach(headerText => {
                    const th = document.createElement('th');
                    th.textContent = headerText;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);

                const tbody = document.createElement('tbody');

                // Add model rows
                categories[categoryName].sort((a, b) => {
                    const nameA = language === 'english' ? a.englishDescription : a.frenchDescription;
                    const nameB = language === 'english' ? b.englishDescription : b.frenchDescription;
                    return nameA.localeCompare(nameB);
                }).forEach(model => {
                    console.log('Processing model:', model); // Debug log
                    const row = document.createElement('tr');

                    // Add inactive class if model is not active
                    if (!model.isActive) {
                        row.classList.add('inactive-model');
                    }

                    // Model name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = language === 'english' ? model.englishDescription : model.frenchDescription;
                    row.appendChild(nameCell);

                    // Start Date
                    const startDateCell = document.createElement('td');
                    startDateCell.textContent = model.startDate || 'N/A';
                    row.appendChild(startDateCell);

                    // End Date
                    const endDateCell = document.createElement('td');
                    endDateCell.textContent = model.endDate || 'N/A';
                    row.appendChild(endDateCell);

                    // Minimum Investment
                    const minInvestmentCell = document.createElement('td');
                    minInvestmentCell.textContent = '$' + (model.minimumInvestment || 0).toLocaleString();
                    row.appendChild(minInvestmentCell);

                    // Status
                    const statusCell = document.createElement('td');
                    const statusSpan = document.createElement('span');
                    statusSpan.className = model.isActive ? 'status-active' : 'status-inactive';
                    statusSpan.textContent = model.isActive ?
                        (language === 'english' ? 'Active' : 'Actif') :
                        (language === 'english' ? 'Inactive' : 'Inactif');
                    statusCell.appendChild(statusSpan);
                    row.appendChild(statusCell);





                    // Actions column
                    console.log('Creating actions column for model ID:', model.modelId); // Debug log
                    const actionsCell = document.createElement('td');
                    actionsCell.className = 'actions-cell';

                    const editBtn = document.createElement('button');
                    editBtn.className = 'btn btn-sm btn-secondary';
                    editBtn.textContent = language === 'english' ? 'Edit' : 'Modifier';
                    editBtn.onclick = () => editModel(model.modelId);

                    const inactivateBtn = document.createElement('button');
                    inactivateBtn.className = 'btn btn-sm btn-danger';
                    inactivateBtn.textContent = language === 'english' ? 'Inactivate' : 'Désactiver';
                    inactivateBtn.onclick = () => inactivateModel(model.modelId, model.englishDescription);
                    inactivateBtn.disabled = !model.isActive;

                    actionsCell.appendChild(editBtn);
                    actionsCell.appendChild(document.createTextNode(' '));
                    actionsCell.appendChild(inactivateBtn);
                    row.appendChild(actionsCell);
                    console.log('Actions column created and appended'); // Debug log

                    tbody.appendChild(row);
                });

                table.appendChild(tbody);
                categoryDiv.appendChild(table);
                modelsContainer.appendChild(categoryDiv);
            });
        }

        // Initialize by fetching models
        fetchModels();

        // Language toggle buttons
        document.getElementById('englishBtn').addEventListener('click', function() {
            currentLanguage = 'english';
            updateLanguageDisplay();
            displayModels('english');
            updateSearchPlaceholder();
        });

        document.getElementById('frenchBtn').addEventListener('click', function() {
            currentLanguage = 'french';
            updateLanguageDisplay();
            displayModels('french');
            updateSearchPlaceholder();
        });

        function updateLanguageDisplay() {
            // Update button states
            document.getElementById('englishBtn').classList.toggle('active', currentLanguage === 'english');
            document.getElementById('frenchBtn').classList.toggle('active', currentLanguage === 'french');

            // Show/hide language-specific text
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            englishElements.forEach(el => {
                el.style.display = currentLanguage === 'english' ? 'inline' : 'none';
            });

            frenchElements.forEach(el => {
                el.style.display = currentLanguage === 'french' ? 'inline' : 'none';
            });
        }

        function updateSearchPlaceholder() {
            const searchInput = document.getElementById('searchInput');
            searchInput.placeholder = currentLanguage === 'english' ? 'Search models...' : 'Rechercher des modèles...';
        }

        // Function to open the add model modal
        function openAddModelModal() {
            const modal = document.getElementById('addModelModal');
            modal.style.display = 'block';

            // Set today's date as default for start date
            document.getElementById('startDate').valueAsDate = new Date();

            // Reset form
            document.getElementById('addModelForm').reset();

            // Reset category dropdowns
            resetCategoryInputs();
        }

        // Make function available globally for sidebar
        window.openAddModelModal = openAddModelModal;

        // Function to reset category inputs to dropdown mode
        function resetCategoryInputs() {
            const englishSelect = document.getElementById('englishCategorySelect');
            const englishInput = document.getElementById('englishCategory');
            const frenchSelect = document.getElementById('frenchCategorySelect');
            const frenchInput = document.getElementById('frenchCategory');

            englishSelect.style.display = 'block';
            englishInput.style.display = 'none';
            englishSelect.value = '';
            englishInput.value = '';

            frenchSelect.style.display = 'block';
            frenchInput.style.display = 'none';
            frenchSelect.value = '';
            frenchInput.value = '';
        }

        // Category dropdown change handlers
        document.addEventListener('DOMContentLoaded', function() {
            const englishSelect = document.getElementById('englishCategorySelect');
            const englishInput = document.getElementById('englishCategory');
            const frenchSelect = document.getElementById('frenchCategorySelect');
            const frenchInput = document.getElementById('frenchCategory');

            englishSelect.addEventListener('change', function() {
                if (this.value === '__new__') {
                    this.style.display = 'none';
                    englishInput.style.display = 'block';
                    englishInput.focus();
                    englishInput.required = true;
                } else if (this.value !== '') {
                    englishInput.value = this.value;
                    englishInput.required = true;
                } else {
                    englishInput.value = '';
                    englishInput.required = true;
                }
            });

            frenchSelect.addEventListener('change', function() {
                if (this.value === '__new__') {
                    this.style.display = 'none';
                    frenchInput.style.display = 'block';
                    frenchInput.focus();
                    frenchInput.required = true;
                } else if (this.value !== '') {
                    frenchInput.value = this.value;
                    frenchInput.required = true;
                } else {
                    frenchInput.value = '';
                    frenchInput.required = true;
                }
            });

            // Allow users to go back to dropdown by clearing the input
            englishInput.addEventListener('blur', function() {
                if (this.value === '') {
                    this.style.display = 'none';
                    englishSelect.style.display = 'block';
                    englishSelect.value = '';
                }
            });

            frenchInput.addEventListener('blur', function() {
                if (this.value === '') {
                    this.style.display = 'none';
                    frenchSelect.style.display = 'block';
                    frenchSelect.value = '';
                }
            });
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();

            if (searchTerm === '') {
                displayModels(currentLanguage);
                return;
            }

            // Filter models based on search term
            const filteredModels = allModels.filter(model => {
                const englishDesc = model.englishDescription.toLowerCase();
                const frenchDesc = model.frenchDescription.toLowerCase();
                const englishCat = model.englishCategory.toLowerCase();
                const frenchCat = model.frenchCategory.toLowerCase();

                return englishDesc.includes(searchTerm) ||
                       frenchDesc.includes(searchTerm) ||
                       englishCat.includes(searchTerm) ||
                       frenchCat.includes(searchTerm);
            });

            // Update display with filtered models
            const modelsContainer = document.getElementById('modelsContainer');
            modelsContainer.innerHTML = '';

            if (filteredModels.length === 0) {
                modelsContainer.innerHTML = '<p>No models found matching your search.</p>';
                return;
            }

            // Group filtered models by category
            const categories = {};
            filteredModels.forEach(model => {
                const categoryName = currentLanguage === 'english' ? model.englishCategory : model.frenchCategory;
                if (!categories[categoryName]) {
                    categories[categoryName] = [];
                }
                categories[categoryName].push(model);
            });

            // Display filtered models
            Object.keys(categories).sort().forEach(categoryName => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'category';

                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'category-header';
                categoryHeader.textContent = categoryName;
                categoryDiv.appendChild(categoryHeader);

                // Add table with filtered models (similar to displayModels)
                // Implementation abbreviated for brevity

                modelsContainer.appendChild(categoryDiv);
            });
        });

        // Navigation is now handled by the sidebar links directly

        // Modal functionality
        const modal = document.getElementById('addModelModal');
        const closeBtn = document.querySelector('.close');
        const cancelBtn = document.getElementById('cancelBtn');

        // Modal is now opened via the openAddModelModal() function called from sidebar

        // Close modal when X is clicked
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when Cancel button is clicked
        cancelBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside of it
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });



        // Form submission
        const addModelForm = document.getElementById('addModelForm');

        addModelForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Check if we're in edit mode
            const isEditMode = this.dataset.editMode === 'true';
            const modelId = this.dataset.modelId;

            // Validate form
            if (!this.checkValidity()) {
                return;
            }

            // Prepare model data
            const modelData = {
                englishDescription: document.getElementById('englishDescription').value.trim(),
                frenchDescription: document.getElementById('frenchDescription').value.trim(),
                englishCategory: document.getElementById('englishCategory').value.trim(),
                frenchCategory: document.getElementById('frenchCategory').value.trim(),
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value || null,
                minimumInvestment: parseFloat(document.getElementById('minimumInvestment').value) || 0,
                isActive: true,
                fixedIncome: 0,
                equity: 0,
                alternatives: 0
            };

            try {
                const url = isEditMode ? `/api/models/${modelId}` : '/api/models';
                const method = isEditMode ? 'PUT' : 'POST';

                console.log(`Sending ${method} request to ${url}`);
                console.log('Model data:', JSON.stringify(modelData));

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(modelData)
                });

                const result = await response.json();

                if (response.ok) {
                    // Show success message
                    const successContainer = document.getElementById('successContainer');
                    successContainer.textContent = isEditMode
                        ? (currentLanguage === 'english' ? 'Model updated successfully!' : 'Modèle mis à jour avec succès!')
                        : (currentLanguage === 'english' ? 'Model added successfully!' : 'Modèle ajouté avec succès!');
                    successContainer.style.display = 'block';

                    // Hide success message after 5 seconds
                    setTimeout(() => {
                        successContainer.style.display = 'none';
                    }, 5000);

                    // Reset form and close modal
                    this.reset();
                    this.dataset.editMode = 'false';
                    delete this.dataset.modelId;

                    // Reset modal title and button text
                    const modalTitle = document.querySelector('#addModelModal h2');
                    modalTitle.innerHTML = `
                        <span class="english-text">Add New Model</span>
                        <span class="french-text" style="display: none;">Ajouter un Nouveau Modèle</span>
                    `;

                    const saveBtn = document.getElementById('saveModelBtn');
                    saveBtn.innerHTML = `
                        <span class="english-text">Save Model</span>
                        <span class="french-text" style="display: none;">Enregistrer le Modèle</span>
                    `;

                    document.getElementById('addModelModal').style.display = 'none';

                    // Refresh models list
                    fetchModels();
                } else {
                    throw new Error(result.error || 'Failed to save model');
                }
            } catch (error) {
                console.error('Error saving model:', error);

                // Show error message
                const errorContainer = document.getElementById('errorContainer');
                errorContainer.textContent = error.message;
                errorContainer.style.display = 'block';

                // Hide error message after 5 seconds
                setTimeout(() => {
                    errorContainer.style.display = 'none';
                }, 5000);
            }
        });
    </script>

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the sidebar functionality (HTML is now inline)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar functionality
            if (typeof initializeSidebar === 'function') {
                initializeSidebar();
            }
            if (typeof initializeLanguageSwitching === 'function') {
                initializeLanguageSwitching();
            }
            if (typeof setActiveNavItem === 'function') {
                setActiveNavItem();
            }
            if (typeof updateLanguageDisplay === 'function') {
                updateLanguageDisplay();
            }
        });



        // CRUD Functions for Models
        function editModel(modelId) {
            // Find the model in allModels array
            const model = allModels.find(m => m.modelId === modelId);
            if (!model) {
                alert('Model not found');
                return;
            }

            // Populate the form with existing model data
            document.getElementById('englishDescription').value = model.englishDescription;
            document.getElementById('frenchDescription').value = model.frenchDescription;

            // Handle category dropdowns for editing
            const englishSelect = document.getElementById('englishCategorySelect');
            const englishInput = document.getElementById('englishCategory');
            const frenchSelect = document.getElementById('frenchCategorySelect');
            const frenchInput = document.getElementById('frenchCategory');

            // Check if the category exists in dropdown, if not, show input field
            const englishCategoryExists = Array.from(englishSelect.options).some(option => option.value === model.englishCategory);
            if (englishCategoryExists) {
                englishSelect.value = model.englishCategory;
                englishInput.value = model.englishCategory;
                englishSelect.style.display = 'block';
                englishInput.style.display = 'none';
            } else {
                englishSelect.style.display = 'none';
                englishInput.style.display = 'block';
                englishInput.value = model.englishCategory;
            }

            const frenchCategoryExists = Array.from(frenchSelect.options).some(option => option.value === model.frenchCategory);
            if (frenchCategoryExists) {
                frenchSelect.value = model.frenchCategory;
                frenchInput.value = model.frenchCategory;
                frenchSelect.style.display = 'block';
                frenchInput.style.display = 'none';
            } else {
                frenchSelect.style.display = 'none';
                frenchInput.style.display = 'block';
                frenchInput.value = model.frenchCategory;
            }

            document.getElementById('startDate').value = model.startDate;
            document.getElementById('endDate').value = model.endDate || '';
            document.getElementById('minimumInvestment').value = model.minimumInvestment || 0;

            // Change form to edit mode
            const form = document.getElementById('addModelForm');
            form.dataset.editMode = 'true';
            form.dataset.modelId = modelId;

            // Update modal title
            const modalTitle = document.querySelector('#addModelModal h2');
            modalTitle.innerHTML = `
                <span class="english-text">Edit Model</span>
                <span class="french-text" style="display: none;">Modifier le Modèle</span>
            `;

            // Update button text
            const saveBtn = document.getElementById('saveModelBtn');
            saveBtn.innerHTML = `
                <span class="english-text">Update Model</span>
                <span class="french-text" style="display: none;">Mettre à Jour le Modèle</span>
            `;

            // Show modal
            document.getElementById('addModelModal').style.display = 'block';
        }

        async function inactivateModel(modelId, modelName) {
            const confirmMessage = currentLanguage === 'english'
                ? `Are you sure you want to inactivate the model "${modelName}"?`
                : `Êtes-vous sûr de vouloir désactiver le modèle "${modelName}"?`;

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch(`/api/models/${modelId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    // Show success message
                    const successContainer = document.getElementById('successContainer');
                    successContainer.textContent = currentLanguage === 'english'
                        ? 'Model inactivated successfully!'
                        : 'Modèle désactivé avec succès!';
                    successContainer.style.display = 'block';

                    // Hide success message after 5 seconds
                    setTimeout(() => {
                        successContainer.style.display = 'none';
                    }, 5000);

                    // Refresh models list
                    fetchModels();
                } else {
                    throw new Error(result.error || 'Failed to inactivate model');
                }
            } catch (error) {
                console.error('Error inactivating model:', error);

                // Show error message
                const errorContainer = document.getElementById('errorContainer');
                errorContainer.textContent = error.message;
                errorContainer.style.display = 'block';

                // Hide error message after 5 seconds
                setTimeout(() => {
                    errorContainer.style.display = 'none';
                }, 5000);
            }
        }


    </script>
</body>
</html>
