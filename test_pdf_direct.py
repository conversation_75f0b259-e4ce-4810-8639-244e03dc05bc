#!/usr/bin/env python3
"""
Test PDF generation directly without server
"""

import sys
import os

# Add current directory to path to import from ipp_uma_server
sys.path.insert(0, os.getcwd())

try:
    from ipp_uma_server import generate_ips_pdf
    print("✅ Successfully imported generate_ips_pdf function")
except ImportError as e:
    print(f"❌ Failed to import: {e}")
    sys.exit(1)

def test_pdf_generation():
    """Test PDF generation directly"""
    print("🧪 Testing PDF generation directly...")
    
    # Sample IPS data
    ips_data = {
        "clientName": "<PERSON>",
        "accountNumber": "ACC123456",
        "repCode": "REP001",
        "advisorName": "Jane Advisor",
        "accountType": "Individual",
        "programType": "UMA",
        "riskCapacityLevel": "Moderate",
        "riskToleranceLevel": "Moderate",
        "ipqMethod": "Questionnaire",
        "riskProfileOverride": False,
        "selectedModels": [
            {
                "modelId": 3,
                "allocation": 60,
                "model": {
                    "englishDescription": "Elite Core Balanced",
                    "frenchDescription": "Elite équilibrée de base",
                    "fixedIncome": 40,
                    "equity": 50,
                    "alternatives": 10
                }
            },
            {
                "modelId": 4,
                "allocation": 40,
                "model": {
                    "englishDescription": "Elite Growth",
                    "frenchDescription": "Elite Croissance",
                    "fixedIncome": 20,
                    "equity": 70,
                    "alternatives": 10
                }
            }
        ],
        "status": "Generated",
        "createdDate": "2025-06-03T13:00:00.000Z"
    }
    
    try:
        print("📄 Generating PDF...")
        pdf_buffer = generate_ips_pdf(ips_data)
        
        if pdf_buffer:
            # Save the PDF
            filename = "test_direct_ips.pdf"
            with open(filename, 'wb') as f:
                f.write(pdf_buffer.getvalue())
            
            file_size = os.path.getsize(filename)
            print(f"✅ PDF generated successfully!")
            print(f"💾 Saved as: {filename}")
            print(f"📄 File size: {file_size} bytes")
            
            if file_size > 1000:  # Should be much larger than 77 bytes
                print("✅ PDF appears to have proper content")
                return True
            else:
                print("⚠️  PDF file seems too small")
                return False
        else:
            print("❌ PDF generation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error generating PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("🧪 DIRECT PDF GENERATION TEST")
    print("=" * 50)
    
    success = test_pdf_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 PDF GENERATION WORKS!")
    else:
        print("❌ PDF GENERATION FAILED")
    print("=" * 50)
