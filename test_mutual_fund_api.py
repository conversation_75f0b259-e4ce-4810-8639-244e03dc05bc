#!/usr/bin/env python3
"""
Test script for mutual fund API endpoints
"""

import requests
import json

def test_add_mutual_fund():
    """Test adding a new mutual fund using unified models API"""
    print("🧪 Testing Add Mutual Fund API (unified endpoint)...")

    # Sample mutual fund data (now using unified models endpoint)
    fund_data = {
        "fundCode": "TEST001",
        "englishName": "Test Growth Fund",
        "frenchName": "Fonds de Croissance Test",
        "fundType": "Equity",
        "assetClass": "Canadian Equity",
        "startDate": "2025-06-03",
        "isActive": True
    }

    try:
        response = requests.post(
            'http://localhost:8000/api/models',  # Now using unified endpoint
            headers={'Content-Type': 'application/json'},
            json=fund_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Mutual fund added successfully!")
            print(f"   Fund ID: {result.get('fundId')}")
            return True
        else:
            print(f"❌ Failed to add mutual fund: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_get_models_with_funds():
    """Test getting all models including mutual funds"""
    print("\n🧪 Testing Get Models API (including mutual funds)...")
    
    try:
        response = requests.get('http://localhost:8000/api/models')
        
        if response.status_code == 200:
            models = response.json()
            
            # Count different types
            regular_models = [m for m in models if m.get('modelType') == 'model']
            mutual_funds = [m for m in models if m.get('modelType') == 'mutual_fund']
            
            print(f"✅ API Response successful")
            print(f"📊 Total models returned: {len(models)}")
            print(f"🏢 Regular models: {len(regular_models)}")
            print(f"🏦 Mutual funds: {len(mutual_funds)}")
            
            if mutual_funds:
                print("\n🏦 Mutual funds found:")
                for fund in mutual_funds[:5]:  # Show first 5
                    print(f"   ID: {fund['modelId']}")
                    print(f"   Name: {fund['englishDescription']}")
                    print(f"   Category: {fund['englishCategory']}")
                    print(f"   Fund Code: {fund.get('fundCode', 'N/A')}")
                    print(f"   Asset Class: {fund.get('assetClass', 'N/A')}")
                    print()
            else:
                print("⚠️  No mutual funds found in API response")
                
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Connection Error: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 MUTUAL FUND API TESTING")
    print("=" * 60)
    
    # Test 1: Get existing models and funds
    success1 = test_get_models_with_funds()
    
    # Test 2: Add a new mutual fund
    success2 = test_add_mutual_fund()
    
    # Test 3: Get models again to see the new fund
    if success2:
        print("\n🔄 Checking if new fund appears in models list...")
        success3 = test_get_models_with_funds()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Mutual funds are working correctly")
        print("✅ They appear as selectable models in the API")
        print("✅ New mutual funds can be added successfully")
    else:
        print("❌ SOME TESTS FAILED")
        print("Please check the server logs for errors")
    print("=" * 60)

if __name__ == '__main__':
    main()
