#!/usr/bin/env python3
import sqlite3

def fix_model_types():
    conn = sqlite3.connect('ipp_uma_models.db')
    cursor = conn.cursor()
    
    # Update NULL or empty ModelType to 'model'
    cursor.execute("UPDATE tblModels SET ModelType = 'model' WHERE ModelType IS NULL OR ModelType = ''")
    updated = cursor.rowcount
    
    conn.commit()
    conn.close()
    
    print(f"Updated {updated} records to have ModelType = 'model'")

if __name__ == '__main__':
    fix_model_types()
