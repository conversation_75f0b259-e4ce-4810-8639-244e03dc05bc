<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Fee Analysis - IPP-UMA System</title>



    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Main content with sidebar */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top bar */
        .top-bar {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px 35px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-bar h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .top-bar .language-toggle button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .top-bar .language-toggle button:hover {
            background: #3b82f6;
        }

        /* Clean page-specific styles - no conflicts with sidebar */
        .blended-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px;
        }

        .blended-form-group {
            margin-bottom: 20px;
        }

        .blended-form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .blended-container select,
        .blended-container input[type="number"] {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .blended-container select:focus,
        .blended-container input[type="number"]:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .blended-btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 12px 24px;
            font-size: 16px;
            line-height: 1.5;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            background-color: #28a745;
            color: white;
        }

        .blended-btn:hover {
            background-color: #218838;
            transform: translateY(-1px);
        }

        .blended-btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .blended-btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-1px);
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .hidden {
            display: none;
        }
        .model-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .model-select {
            flex: 3;
            margin-right: 10px;
        }
        .weight-input {
            flex: 1;
            margin-right: 10px;
        }
        .remove-btn {
            flex: 0 0 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .add-model-btn {
            margin-top: 10px;
        }
        .total-weight {
            margin-top: 15px;
            font-weight: bold;
        }
        .weight-warning {
            color: #dc3545;
            display: none;
        }
        .chart-container {
            margin-top: 30px;
            height: 300px;
        }

        /* Page-specific styles */
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #6c757d;
            font-size: 1.1em;
        }

        .nav-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: #0056b3;
        }
    </style>

    <!-- Import sidebar CSS AFTER embedded styles to ensure proper precedence -->
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="add_model.html" class="nav-item create-action" id="addModelNav">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item active" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="window.location.href='add_model.html'" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Portfolio Fee Analysis</span>
                <span class="french-text">Analyse des Frais de Portefeuille</span>
            </h1>
            <div class="language-toggle">
                <button id="topEnglishBtn" class="active">English</button>
                <button id="topFrenchBtn">Français</button>
            </div>
        </div>

        <div class="blended-container">
            <div class="header">
                <h1>IPP-UMA Blended Fee Calculator</h1>
                <p>Calculate weighted average fees across multiple UMA models</p>
            </div>

        <div id="modelSelectionContainer">
            <h2 id="selectionTitle">Select Models and Weights</h2>
            <p id="selectionInstructions">Add models and assign weights (%) to calculate a blended fee.</p>

            <div id="modelRows">
                <!-- Model rows will be added here dynamically -->
                <div class="model-row" id="model-row-1">
                    <div class="model-select">
                        <select id="model-1" class="model-dropdown" onchange="updateModelInfo(1)">
                            <option value="">-- Select a model --</option>
                        </select>
                    </div>
                    <div class="weight-input">
                        <input type="number" id="weight-1" class="weight" min="0" max="100" value="100" onchange="updateTotalWeight()">
                    </div>
                    <button class="remove-btn blended-btn blended-btn-danger" onclick="removeModelRow(1)">×</button>
                </div>
            </div>

            <button class="add-model-btn blended-btn" onclick="addModelRow()" id="addModelBtn">Add Model</button>

            <div class="blended-form-group">
                <label for="investmentAmount" id="investmentAmountLabel">Investment Amount ($):</label>
                <input type="number" id="investmentAmount" min="0" step="1000" value="500000">
            </div>

            <div class="total-weight">
                <span id="totalWeightLabel">Total Weight:</span> <span id="totalWeightValue">100</span>%
                <div class="weight-warning" id="weightWarning">Total weight must be 100%</div>
            </div>

            <button onclick="calculateBlendedFees()" id="calculateBtn" class="blended-btn">Calculate Blended Fees</button>
        </div>

        <div id="results" class="results hidden">
            <h2 id="resultsTitle">Blended Fee Calculation Results</h2>

            <div id="blendedResultsContent">
                <!-- Blended results will be displayed here -->
            </div>

            <div id="modelBreakdown">
                <h3 id="breakdownTitle">Model Breakdown</h3>
                <table id="breakdownTable">
                    <thead>
                        <tr>
                            <th id="modelHeader">Model</th>
                            <th id="weightHeader">Weight</th>
                            <th id="tierHeader">Fee Tier</th>
                            <th id="adminCostHeader">Admin Cost (%)</th>
                            <th id="advisorFeeHeader">Advisor Fee (%)</th>
                            <th id="totalFeeHeader">Total Fee (%)</th>
                            <th id="weightedFeeHeader">Weighted Fee (%)</th>
                        </tr>
                    </thead>
                    <tbody id="breakdownBody"></tbody>
                </table>
            </div>

            <div class="chart-container">
                <canvas id="feeChart"></canvas>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let currentLanguage = 'en';
        let models = [];
        let feePrograms = [];
        let feeTiers = [];
        let nextRowId = 2;
        let chart = null;

        // Load data when page loads
        window.onload = function() {
            loadModels();
            loadFeePrograms();
            loadFeeTiers();
        };

        function loadModels() {
            fetch('/api/models')
                .then(response => response.json())
                .then(data => {
                    models = data;
                    updateModelDropdowns();
                })
                .catch(error => console.error('Error loading models:', error));
        }

        function loadFeePrograms() {
            fetch('/api/fee-programs')
                .then(response => response.json())
                .then(data => {
                    feePrograms = data;
                })
                .catch(error => console.error('Error loading fee programs:', error));
        }

        function loadFeeTiers() {
            fetch('/api/fee-tiers')
                .then(response => response.json())
                .then(data => {
                    feeTiers = data;
                })
                .catch(error => console.error('Error loading fee tiers:', error));
        }

        function updateModelDropdowns() {
            const dropdowns = document.querySelectorAll('.model-dropdown');

            dropdowns.forEach(dropdown => {
                const selectedValue = dropdown.value;

                // Clear existing options except the first one
                while (dropdown.options.length > 1) {
                    dropdown.remove(1);
                }

                // Add models to dropdown (always use English for simplicity)
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.modelId;
                    option.textContent = model.englishDescription;
                    dropdown.appendChild(option);
                });

                // Restore selected value if possible
                if (selectedValue) {
                    dropdown.value = selectedValue;
                }
            });
        }

        function addModelRow() {
            const rowId = nextRowId++;
            const modelRows = document.getElementById('modelRows');

            const newRow = document.createElement('div');
            newRow.className = 'model-row';
            newRow.id = `model-row-${rowId}`;

            newRow.innerHTML = `
                <div class="model-select">
                    <select id="model-${rowId}" class="model-dropdown" onchange="updateModelInfo(${rowId})">
                        <option value="">-- Select a model --</option>
                    </select>
                </div>
                <div class="weight-input">
                    <input type="number" id="weight-${rowId}" class="weight" min="0" max="100" value="0" onchange="updateTotalWeight()">
                </div>
                <button class="remove-btn blended-btn blended-btn-danger" onclick="removeModelRow(${rowId})">×</button>
            `;

            modelRows.appendChild(newRow);
            updateModelDropdowns();
            updateTotalWeight();
        }

        function removeModelRow(rowId) {
            const modelRows = document.getElementById('modelRows');
            const row = document.getElementById(`model-row-${rowId}`);

            // Don't remove if it's the last row
            if (modelRows.children.length > 1) {
                modelRows.removeChild(row);
                updateTotalWeight();
            } else {
                // If it's the last row, just reset it
                document.getElementById(`model-${rowId}`).value = '';
                document.getElementById(`weight-${rowId}`).value = '100';
                updateTotalWeight();
            }
        }

        function updateModelInfo(rowId) {
            // This function can be used to update model-specific information
            // when a model is selected
        }

        function updateTotalWeight() {
            const weightInputs = document.querySelectorAll('.weight');
            let totalWeight = 0;

            weightInputs.forEach(input => {
                totalWeight += parseFloat(input.value) || 0;
            });

            const totalWeightValue = document.getElementById('totalWeightValue');
            totalWeightValue.textContent = totalWeight.toFixed(2);

            const weightWarning = document.getElementById('weightWarning');
            if (Math.abs(totalWeight - 100) > 0.01) {
                weightWarning.style.display = 'block';
                totalWeightValue.style.color = '#dc3545';
            } else {
                weightWarning.style.display = 'none';
                totalWeightValue.style.color = 'inherit';
            }
        }

        function calculateBlendedFees() {
            // Get all model rows
            const modelRows = document.querySelectorAll('.model-row');
            const selectedModels = [];

            // Get investment amount
            const investmentAmount = parseFloat(document.getElementById('investmentAmount').value);
            if (isNaN(investmentAmount) || investmentAmount <= 0) {
                alert(currentLanguage === 'en' ?
                    'Please enter a valid investment amount' :
                    'Veuillez entrer un montant d\'investissement valide');
                return;
            }

            // Validate total weight
            const totalWeight = parseFloat(document.getElementById('totalWeightValue').textContent);
            if (Math.abs(totalWeight - 100) > 0.01) {
                alert(currentLanguage === 'en' ?
                    'Total weight must be 100%' :
                    'La pondération totale doit être de 100%');
                return;
            }

            // Collect selected models and weights
            modelRows.forEach(row => {
                const rowId = row.id.split('-')[2];
                const modelId = document.getElementById(`model-${rowId}`).value;
                const weight = parseFloat(document.getElementById(`weight-${rowId}`).value) || 0;

                if (modelId && weight > 0) {
                    const model = models.find(m => m.modelId == modelId);
                    selectedModels.push({
                        model: model,
                        weight: weight
                    });
                }
            });

            if (selectedModels.length === 0) {
                alert(currentLanguage === 'en' ?
                    'Please select at least one model' :
                    'Veuillez sélectionner au moins un modèle');
                return;
            }

            // Calculate fees for each model
            const modelFees = selectedModels.map(item => {
                const model = item.model;
                const weight = item.weight;

                // Find the fee program for the model
                const feeProgram = feePrograms.find(fp => fp.englishName === model.programType);

                if (!feeProgram) {
                    console.error(`Fee program not found for model: ${model.englishDescription}`);
                    return null;
                }

                // Get the fee tiers for the program
                const programTiers = feeTiers.filter(ft => ft.programId === feeProgram.programId);

                if (programTiers.length === 0) {
                    console.error(`Fee tiers not found for program: ${feeProgram.englishName}`);
                    return null;
                }

                // Find the applicable tier for the investment amount
                const applicableTier = programTiers.find(tier =>
                    investmentAmount >= tier.startAmount && investmentAmount <= tier.endAmount
                );

                if (!applicableTier) {
                    console.error(`No applicable fee tier found for investment amount: ${investmentAmount}`);
                    return null;
                }

                const adminCost = applicableTier.adminCost;
                const advisorFee = applicableTier.advisorFee;
                const totalFee = adminCost + advisorFee;
                const weightedFee = totalFee * (weight / 100);

                return {
                    model: model,
                    weight: weight,
                    adminCost: adminCost,
                    advisorFee: advisorFee,
                    totalFee: totalFee,
                    weightedFee: weightedFee,
                    tier: applicableTier
                };
            }).filter(item => item !== null);

            if (modelFees.length === 0) {
                alert(currentLanguage === 'en' ?
                    'Could not calculate fees for any of the selected models' :
                    'Impossible de calculer les frais pour aucun des modèles sélectionnés');
                return;
            }

            // Calculate blended fees
            const blendedAdminCost = modelFees.reduce((sum, item) => sum + (item.adminCost * item.weight / 100), 0);
            const blendedAdvisorFee = modelFees.reduce((sum, item) => sum + (item.advisorFee * item.weight / 100), 0);
            const blendedTotalFee = blendedAdminCost + blendedAdvisorFee;

            // Calculate annual fee amount
            const annualFeeAmount = investmentAmount * blendedTotalFee / 100;

            // Display results
            displayResults(modelFees, blendedAdminCost, blendedAdvisorFee, blendedTotalFee, investmentAmount, annualFeeAmount);
        }

        function displayResults(modelFees, blendedAdminCost, blendedAdvisorFee, blendedTotalFee, investmentAmount, annualFeeAmount) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.classList.remove('hidden');

            // Display blended results
            const blendedResultsContent = document.getElementById('blendedResultsContent');
            blendedResultsContent.innerHTML = `
                <div class="blended-results">
                    <p><strong>${currentLanguage === 'en' ? 'Investment Amount' : 'Montant de l\'investissement'}:</strong> $${investmentAmount.toLocaleString()}</p>
                    <p><strong>${currentLanguage === 'en' ? 'Blended Admin Cost' : 'Coût administratif mélangé'}:</strong> ${blendedAdminCost.toFixed(2)}%</p>
                    <p><strong>${currentLanguage === 'en' ? 'Blended Advisor Fee' : 'Frais de conseiller mélangés'}:</strong> ${blendedAdvisorFee.toFixed(2)}%</p>
                    <p><strong>${currentLanguage === 'en' ? 'Blended Total Fee' : 'Frais total mélangé'}:</strong> ${blendedTotalFee.toFixed(2)}%</p>
                    <p><strong>${currentLanguage === 'en' ? 'Annual Fee Amount' : 'Montant annuel des frais'}:</strong> $${annualFeeAmount.toLocaleString(undefined, {maximumFractionDigits: 2})}</p>
                </div>
            `;

            // Display model breakdown
            const breakdownBody = document.getElementById('breakdownBody');
            breakdownBody.innerHTML = '';

            modelFees.forEach(item => {
                const row = document.createElement('tr');

                // Model name
                const nameCell = document.createElement('td');
                nameCell.textContent = currentLanguage === 'en' ? item.model.englishDescription : item.model.frenchDescription;
                row.appendChild(nameCell);

                // Weight
                const weightCell = document.createElement('td');
                weightCell.textContent = `${item.weight.toFixed(2)}%`;
                row.appendChild(weightCell);

                // Fee tier
                const tierCell = document.createElement('td');
                tierCell.textContent = `$${item.tier.startAmount.toLocaleString()} - $${item.tier.endAmount.toLocaleString()}`;
                row.appendChild(tierCell);

                // Admin cost
                const adminCostCell = document.createElement('td');
                adminCostCell.textContent = `${item.adminCost.toFixed(2)}%`;
                row.appendChild(adminCostCell);

                // Advisor fee
                const advisorFeeCell = document.createElement('td');
                advisorFeeCell.textContent = `${item.advisorFee.toFixed(2)}%`;
                advisorFeeCell.style.backgroundColor = 'white';
                advisorFeeCell.style.color = 'black';
                row.appendChild(advisorFeeCell);

                // Total fee
                const totalFeeCell = document.createElement('td');
                totalFeeCell.textContent = `${item.totalFee.toFixed(2)}%`;
                row.appendChild(totalFeeCell);

                // Weighted fee
                const weightedFeeCell = document.createElement('td');
                weightedFeeCell.textContent = `${item.weightedFee.toFixed(2)}%`;
                row.appendChild(weightedFeeCell);

                breakdownBody.appendChild(row);
            });

            // Create chart
            createChart(modelFees, blendedAdminCost, blendedAdvisorFee);
        }

        function createChart(modelFees, blendedAdminCost, blendedAdvisorFee) {
            const ctx = document.getElementById('feeChart').getContext('2d');

            // Destroy previous chart if it exists
            if (chart) {
                chart.destroy();
            }

            // Prepare data for chart
            const labels = modelFees.map(item =>
                currentLanguage === 'en' ? item.model.englishDescription : item.model.frenchDescription
            );

            const adminCostData = modelFees.map(item => item.adminCost);
            const advisorFeeData = modelFees.map(item => item.advisorFee);

            // Add blended result to the chart
            labels.push(currentLanguage === 'en' ? 'BLENDED RESULT' : 'RÉSULTAT MÉLANGÉ');
            adminCostData.push(blendedAdminCost);
            advisorFeeData.push(blendedAdvisorFee);

            chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: currentLanguage === 'en' ? 'Admin Cost' : 'Coût administratif',
                            data: adminCostData,
                            backgroundColor: 'rgba(54, 162, 235, 0.7)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: currentLanguage === 'en' ? 'Advisor Fee' : 'Frais de conseiller',
                            data: advisorFeeData,
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderColor: 'rgba(200, 200, 200, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: currentLanguage === 'en' ? 'Fee (%)' : 'Frais (%)'
                            }
                        }
                    }
                }
            });
        }

        // Language toggle function
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fr' : 'en';
            updateLanguageDisplay();
        }

        function updateLanguageDisplay() {
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            if (currentLanguage === 'en') {
                englishElements.forEach(el => el.style.display = 'inline');
                frenchElements.forEach(el => el.style.display = 'none');
            } else {
                englishElements.forEach(el => el.style.display = 'none');
                frenchElements.forEach(el => el.style.display = 'inline');
            }
        }

        // Initialize language display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
        });
    </script>
    </div> <!-- Close main-content -->

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the sidebar functionality (HTML is now inline)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar functionality
            if (typeof initializeSidebar === 'function') {
                initializeSidebar();
            }
            if (typeof initializeLanguageSwitching === 'function') {
                initializeLanguageSwitching();
            }
            if (typeof setActiveNavItem === 'function') {
                setActiveNavItem();
            }
            if (typeof updateLanguageDisplay === 'function') {
                updateLanguageDisplay();
            }
        });
    </script>
</body>
</html>
