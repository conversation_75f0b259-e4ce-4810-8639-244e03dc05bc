import sqlite3

conn = sqlite3.connect('ipp_uma_models.db')
cursor = conn.cursor()

# Insert sample data
sample_funds = [
    ('FUND001', 'Canadian Equity Fund', 'Fonds actions canadiennes', 'Equity', 'Canadian Equity', '2020-01-01', 1),
    ('FUND002', 'US Equity Fund', 'Fonds actions americaines', 'Equity', 'US Equity', '2020-01-01', 1),
    ('FUND003', 'Bond Fund', 'Fonds obligations', 'Fixed Income', 'Bonds', '2020-01-01', 1)
]

for fund in sample_funds:
    try:
        cursor.execute("""
            INSERT OR IGNORE INTO tblMutualFunds (FundCode, EnglishName, FrenchName, FundType, AssetClass, StartDate, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, fund)
    except Exception as e:
        print(f"Error inserting {fund[0]}: {e}")

conn.commit()
conn.close()
print("Sample funds added successfully")
