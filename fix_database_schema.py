#!/usr/bin/env python3
"""
Fix database schema to allow NULL values for RepCode
"""

import sqlite3

def fix_database_schema():
    """Fix the database schema to allow NULL RepCode"""
    try:
        conn = sqlite3.connect('ipp_uma_models.db')
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tblIPSDocuments'")
        if cursor.fetchone():
            print("📋 tblIPSDocuments table exists, dropping it...")
            cursor.execute("DROP TABLE tblIPSDocuments")
        
        # Recreate table with proper schema
        cursor.execute("""
            CREATE TABLE tblIPSDocuments (
                IPSID INTEGER PRIMARY KEY AUTOINCREMENT,
                ClientName TEXT,
                AccountNumber TEXT,
                RepCode TEXT,  -- Allow NULL
                AdvisorName TEXT,
                ProgramType TEXT,
                AccountType TEXT,
                RiskCapacityLevel TEXT,
                RiskToleranceLevel TEXT,
                IPQMethod TEXT,
                Status TEXT DEFAULT 'Generated',
                CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                DocumentData TEXT
            )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ Database schema fixed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database schema: {e}")
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("🔧 FIXING DATABASE SCHEMA")
    print("=" * 50)
    
    success = fix_database_schema()
    
    print("=" * 50)
    if success:
        print("🎉 SCHEMA FIXED!")
    else:
        print("💥 SCHEMA FIX FAILED!")
    print("=" * 50)
