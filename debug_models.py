#!/usr/bin/env python3
import sqlite3

def debug_get_models():
    """Debug version of get_models function"""
    conn = sqlite3.connect('ipp_uma_models.db')
    conn.row_factory = sqlite3.Row
    
    try:
        cursor = conn.cursor()

        # Check if MinimumInvestment column exists and add it if it doesn't
        cursor.execute("PRAGMA table_info(tblModels)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'MinimumInvestment' not in columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN MinimumInvestment REAL DEFAULT 0')
            conn.commit()

        # Get regular models
        print("🔍 Fetching regular models...")
        cursor.execute("""
            SELECT
                ModelID,
                EnglishDescription,
                FrenchDescription,
                EnglishCategory,
                FrenchCategory,
                StartDate,
                EndDate,
                IsActive,
                FixedIncomePercentage,
                EquityPercentage,
                AlternativesPercentage,
                ProgramType,
                COALESCE(MinimumInvestment, 0) as MinimumInvestment
            FROM tblModels
            ORDER BY EnglishCategory, EnglishDescription
        """)

        models = []
        regular_rows = cursor.fetchall()
        print(f"📊 Found {len(regular_rows)} regular models")
        
        for row in regular_rows:
            models.append({
                "modelId": row['ModelID'],
                "englishDescription": row['EnglishDescription'],
                "frenchDescription": row['FrenchDescription'],
                "englishCategory": row['EnglishCategory'],
                "frenchCategory": row['FrenchCategory'],
                "startDate": row['StartDate'],
                "endDate": row['EndDate'],
                "isActive": bool(row['IsActive']),
                "fixedIncome": row['FixedIncomePercentage'] if row['FixedIncomePercentage'] is not None else 0,
                "equity": row['EquityPercentage'] if row['EquityPercentage'] is not None else 0,
                "alternatives": row['AlternativesPercentage'] if row['AlternativesPercentage'] is not None else 0,
                "programType": row['ProgramType'],
                "minimumInvestment": row['MinimumInvestment'],
                "sourceType": "model"
            })

        # Get active mutual funds and add them as selectable models
        print("🔍 Fetching mutual funds...")
        cursor.execute("""
            SELECT
                FundID,
                EnglishName,
                FrenchName,
                FundType,
                AssetClass,
                StartDate,
                EndDate,
                IsActive,
                FundCode
            FROM tblMutualFunds
            WHERE IsActive = 1
            ORDER BY EnglishName
        """)

        fund_rows = cursor.fetchall()
        print(f"🏦 Found {len(fund_rows)} active mutual funds")

        for row in fund_rows:
            print(f"   Processing fund: {row['FundCode']} - {row['EnglishName']}")
            # Create a model entry for each active mutual fund
            models.append({
                "modelId": f"MF_{row['FundID']}",  # Prefix with MF_ to distinguish from regular models
                "englishDescription": row['EnglishName'],
                "frenchDescription": row['FrenchName'],
                "englishCategory": f"Mutual Funds - {row['FundType'] or 'General'}",
                "frenchCategory": f"Fonds Mutuels - {row['FundType'] or 'Général'}",
                "startDate": row['StartDate'],
                "endDate": row['EndDate'],
                "isActive": bool(row['IsActive']),
                "fixedIncome": 0,  # Mutual funds don't have allocation percentages like models
                "equity": 0,
                "alternatives": 0,
                "programType": "UMA",  # Make mutual funds available for UMA selection
                "minimumInvestment": 0,
                "sourceType": "mutual_fund",
                "fundCode": row['FundCode'],
                "assetClass": row['AssetClass']
            })

        print(f"✅ Total models returned: {len(models)}")
        
        # Count by type
        regular_count = len([m for m in models if m.get('sourceType') == 'model'])
        fund_count = len([m for m in models if m.get('sourceType') == 'mutual_fund'])
        
        print(f"📊 Regular models: {regular_count}")
        print(f"🏦 Mutual funds: {fund_count}")
        
        return models

    except Exception as e:
        print(f"❌ Error getting models: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

    finally:
        conn.close()

if __name__ == "__main__":
    result = debug_get_models()
    if isinstance(result, list):
        print(f"\n✅ Success! Returned {len(result)} total models")
        
        # Show sample mutual funds
        mutual_funds = [m for m in result if m.get('sourceType') == 'mutual_fund']
        if mutual_funds:
            print("\n🏦 Sample mutual funds:")
            for fund in mutual_funds[:3]:
                print(f"   {fund['modelId']}: {fund['englishDescription']}")
    else:
        print(f"\n❌ Error: {result}")
