<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Policy Statements - IPP-UMA System</title>
    <link rel="stylesheet" href="sidebar.css">



    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Main content without sidebar */
        .main-content {
            width: 100%;
            min-height: 100vh;
            background: #f8fafc;
        }

        /* Top bar */
        .top-bar {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px 35px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-bar h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .language-toggle button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .language-toggle button:hover {
            background: #3b82f6;
        }

        /* Language display */
        .english-text {
            display: inline;
        }

        .french-text {
            display: none;
        }

        /* Page-specific styles */
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .progress-bar {
            background-color: #ecf0f1;
            padding: 20px;
            border-bottom: 1px solid #bdc3c7;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #bdc3c7;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
            transition: all 0.3s;
        }

        .step.active .step-number {
            background-color: #3498db;
        }

        .step.completed .step-number {
            background-color: #27ae60;
        }

        .step-title {
            font-size: 0.9em;
            text-align: center;
            color: #7f8c8d;
        }

        .step.active .step-title {
            color: #2c3e50;
            font-weight: bold;
        }

        .step-connector {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background-color: #bdc3c7;
            z-index: -1;
        }

        .step:last-child .step-connector {
            display: none;
        }

        .content {
            padding: 40px;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .radio-option:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }

        .radio-option input[type="radio"] {
            width: auto;
        }

        .radio-option.selected {
            border-color: #3498db;
            background-color: #e3f2fd;
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            margin-right: 10px;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .btn-success {
            background-color: #27ae60;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }

        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert-info {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1976d2;
        }

        .alert-warning {
            background-color: #fff3e0;
            border: 1px solid #ff9800;
            color: #f57c00;
        }

        .alert-success {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #388e3c;
        }

        .card {
            border: 1px solid #ecf0f1;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fafafa;
        }

        .card h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .questionnaire-question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ecf0f1;
            border-radius: 6px;
            background-color: white;
        }

        .question-text {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .answers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .answer-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .answer-option:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }

        .answer-option.selected {
            border-color: #3498db;
            background-color: #e3f2fd;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .hidden {
            display: none;
        }

        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .model-card {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            background-color: white;
            transition: all 0.3s;
        }

        .model-card:hover {
            border-color: #3498db;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .model-card.recommended {
            border-color: #27ae60;
            background-color: #f8fff8;
        }

        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .model-header h4 {
            margin: 0;
            color: #2c3e50;
        }

        .recommended-badge {
            background-color: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .model-details {
            margin-bottom: 15px;
        }

        .allocation-display {
            margin-top: 10px;
        }

        .allocation-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .model-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .allocation-input {
            width: 80px;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .selected-models-list {
            margin-top: 15px;
        }

        .selected-model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #ecf0f1;
            border-radius: 6px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }

        .model-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;
        }

        .review-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ecf0f1;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .review-section h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .review-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .review-label {
            font-weight: bold;
            color: #7f8c8d;
        }

        .review-value {
            color: #2c3e50;
        }

        .strategy-summary, .advisory-summary {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }

        .strategy-summary h4, .advisory-summary h4 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .strategy-summary p, .advisory-summary p {
            margin-bottom: 0;
            color: #7f8c8d;
            font-style: italic;
        }

        /* Page-specific styles */
        .main-container {
            max-width: 1200px;
            margin: 20px;
        }

        .nav-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: #0056b3;
        }

        /* Modal styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* Mutual Fund specific styles */
        .models-section {
            margin-bottom: 30px;
        }

        .models-section h3 {
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .mutual-fund-card {
            border-left: 4px solid #3498db;
            background-color: #f8fbff;
        }

        .mutual-fund-card:hover {
            border-color: #2980b9;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
        }

        .fund-code-badge {
            background-color: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .fund-info {
            margin-top: 10px;
        }

        .fund-type {
            background-color: #e8f4fd;
            color: #2980b9;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <!-- Sidebar content will be generated by JavaScript -->
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Investment Policy Statements</span>
                <span class="french-text">Énoncés de Politique d'Investissement</span>
            </h1>
            <div class="language-toggle">
                <button id="englishBtn" class="active">English</button>
                <button id="frenchBtn">Français</button>
            </div>
        </div>

        <div class="main-container">
            <div class="page-header">
                <h1 class="page-title">
                    <span class="english-text">IPS Generator</span>
                    <span class="french-text">Générateur IPS</span>
                </h1>
                <p class="page-subtitle">
                    <span class="english-text">Investment Policy Statement Creation Tool</span>
                    <span class="french-text">Outil de création d'énoncé de politique d'investissement</span>
                </p>
            </div>

            <div class="container">
                <div class="header">
                    <h1>
                        <span class="english-text">IPS Generator</span>
                        <span class="french-text">Générateur IPS</span>
                    </h1>
                    <p>
                        <span class="english-text">Investment Policy Statement Creation Tool</span>
                        <span class="french-text">Outil de création d'énoncé de politique d'investissement</span>
                    </p>
                </div>

        <div class="progress-bar">
            <div class="progress-steps">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-title">Client Information</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-title">Program Selection</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-title">Risk Assessment</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-title">Model Selection</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-title">Review & Generate</div>
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Step 1: Client Information -->
            <div class="step-content active" id="step-1">
                <h2>Client Information</h2>
                <div class="alert alert-info">
                    Please provide the basic client and account information for the IPS.
                </div>

                <!-- Prospect Selection Section -->
                <div class="form-group">
                    <label for="prospectSelect">Select Existing Prospect (Optional)</label>
                    <select id="prospectSelect" onchange="loadProspectData()">
                        <option value="">-- Select a prospect or enter new client information --</option>
                    </select>
                    <button type="button" class="btn btn-secondary" onclick="showAddProspectModal()" style="margin-left: 10px;">
                        Add New Prospect
                    </button>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="clientName">Client Name *</label>
                        <input type="text" id="clientName" required>
                    </div>
                    <div class="form-group">
                        <label for="accountNumber">Account Number</label>
                        <input type="text" id="accountNumber">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="repCode">Rep Code *</label>
                        <input type="text" id="repCode" required>
                    </div>
                    <div class="form-group">
                        <label for="advisorName">Advisor Name *</label>
                        <input type="text" id="advisorName" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="accountType">Account Type *</label>
                    <select id="accountType" required>
                        <option value="">Select Account Type</option>
                        <option value="Individual">Individual</option>
                        <option value="Joint">Joint</option>
                        <option value="Corporate">Corporate</option>
                    </select>
                </div>
            </div>

            <!-- Navigation will be added by JavaScript -->
            <div class="navigation">
                <button class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">Previous</button>
                <button class="btn" id="nextBtn" onclick="nextStep()">Next</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let totalSteps = 5;
        let ipsData = {};
        let questionsData = [];
        let modelsData = [];

        // Initialize the application
        window.onload = function() {
            loadQuestionsData();
            loadModelsData();
            loadProspects();
        };

        async function loadQuestionsData() {
            try {
                const response = await fetch('/api/ipq-questions');
                if (response.ok) {
                    questionsData = await response.json();
                }
            } catch (error) {
                console.error('Error loading questions:', error);
            }
        }

        async function loadModelsData() {
            try {
                const response = await fetch('/api/models');
                if (response.ok) {
                    modelsData = await response.json();
                }
            } catch (error) {
                console.error('Error loading models:', error);
            }
        }

        function nextStep() {
            if (validateCurrentStep()) {
                saveCurrentStepData();
                if (currentStep < totalSteps) {
                    currentStep++;
                    showStep(currentStep);
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        function showStep(step) {
            // Hide all step contents
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });

            // Show current step content
            const stepContent = document.getElementById(`step-${step}`);
            if (stepContent) {
                stepContent.classList.add('active');
            } else {
                // Create step content dynamically if it doesn't exist
                createStepContent(step);
            }

            // Update progress bar
            updateProgressBar(step);

            // Update navigation buttons
            updateNavigation(step);

            // Load step-specific content
            if (step === 2) {
                loadProgramSelection();
            } else if (step === 3) {
                loadRiskAssessment();
            } else if (step === 4) {
                loadModelSelection();
            } else if (step === 5) {
                loadReviewStep();
            }
        }

        function updateProgressBar(step) {
            document.querySelectorAll('.step').forEach((stepEl, index) => {
                const stepNumber = index + 1;
                stepEl.classList.remove('active', 'completed');

                if (stepNumber === step) {
                    stepEl.classList.add('active');
                } else if (stepNumber < step) {
                    stepEl.classList.add('completed');
                }
            });
        }

        function updateNavigation(step) {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.style.display = step > 1 ? 'inline-block' : 'none';

            if (step === totalSteps) {
                nextBtn.textContent = 'Generate IPS';
                nextBtn.className = 'btn btn-success';
            } else {
                nextBtn.textContent = 'Next';
                nextBtn.className = 'btn';
            }
        }

        function validateCurrentStep() {
            if (currentStep === 1) {
                const clientName = document.getElementById('clientName').value.trim();
                const repCode = document.getElementById('repCode').value.trim();
                const advisorName = document.getElementById('advisorName').value.trim();
                const accountType = document.getElementById('accountType').value;

                if (!clientName || !repCode || !advisorName || !accountType) {
                    alert('Please fill in all required fields.');
                    return false;
                }
            }
            return true;
        }

        function saveCurrentStepData() {
            if (currentStep === 1) {
                ipsData.clientName = document.getElementById('clientName').value.trim();
                ipsData.accountNumber = document.getElementById('accountNumber').value.trim();
                ipsData.repCode = document.getElementById('repCode').value.trim();
                ipsData.advisorName = document.getElementById('advisorName').value.trim();
                ipsData.accountType = document.getElementById('accountType').value;
            }
        }

        function createStepContent(step) {
            const content = document.querySelector('.content');
            const navigation = document.querySelector('.navigation');

            let stepHTML = '';

            if (step === 2) {
                stepHTML = `
                    <div class="step-content active" id="step-2">
                        <h2>Program Selection</h2>
                        <div class="alert alert-info">
                            Select the type of program for this IPS.
                        </div>

                        <div class="form-group">
                            <label>Program Type *</label>
                            <div class="radio-group">
                                <div class="radio-option" onclick="selectProgram('Managed')">
                                    <input type="radio" name="programType" value="Managed" id="managed">
                                    <label for="managed">Discretionary PM Program (Managed)</label>
                                </div>
                                <div class="radio-option" onclick="selectProgram('UMA')">
                                    <input type="radio" name="programType" value="UMA" id="uma">
                                    <label for="uma">Elite UMA (Unified Managed Account)</label>
                                </div>
                                <div class="radio-option" onclick="selectProgram('Non-Managed')">
                                    <input type="radio" name="programType" value="Non-Managed" id="nonmanaged">
                                    <label for="nonmanaged">Fee Based Program (Non-Discretionary)</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Risk Assessment Method *</label>
                            <div class="radio-group">
                                <div class="radio-option" onclick="selectIPQMethod('Internal')">
                                    <input type="radio" name="ipqMethod" value="Internal" id="internal">
                                    <label for="internal">Complete Internal IPQ</label>
                                </div>
                                <div class="radio-option" onclick="selectIPQMethod('External')">
                                    <input type="radio" name="ipqMethod" value="External" id="external">
                                    <label for="external">External IPQ (Upload Document)</label>
                                </div>
                                <div class="radio-option" onclick="selectIPQMethod('Discussion')">
                                    <input type="radio" name="ipqMethod" value="Discussion" id="discussion">
                                    <label for="discussion">Advisor Discussion (No Formal IPQ)</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group hidden" id="externalUpload">
                            <label for="externalDocument">Upload External IPQ Document</label>
                            <input type="file" id="externalDocument" accept=".pdf,.doc,.docx">
                        </div>

                        <div class="form-group hidden" id="advisorNotes">
                            <label for="notes">Advisor Notes</label>
                            <textarea id="notes" rows="4" placeholder="Please provide details about your discussion with the client regarding their risk profile and investment objectives..."></textarea>
                        </div>
                    </div>
                `;
            } else if (step === 3) {
                stepHTML = `
                    <div class="step-content active" id="step-3">
                        <h2>Risk Assessment</h2>
                        <div id="questionnaire-content">
                            <div class="loading">Loading questionnaire...</div>
                        </div>
                    </div>
                `;
            } else if (step === 4) {
                stepHTML = `
                    <div class="step-content active" id="step-4">
                        <h2>Model Selection</h2>
                        <div id="model-selection-content">
                            <div class="loading">Loading available models...</div>
                        </div>
                    </div>
                `;
            } else if (step === 5) {
                stepHTML = `
                    <div class="step-content active" id="step-5">
                        <h2>Review & Generate IPS</h2>
                        <div id="review-content">
                            <div class="loading">Preparing IPS review...</div>
                        </div>
                    </div>
                `;
            }

            if (stepHTML) {
                content.insertBefore(
                    document.createRange().createContextualFragment(stepHTML),
                    navigation
                );
            }
        }

        function selectProgram(programType) {
            ipsData.programType = programType;

            // Update radio button selection
            document.querySelectorAll('input[name="programType"]').forEach(radio => {
                radio.checked = radio.value === programType;
                radio.closest('.radio-option').classList.toggle('selected', radio.checked);
            });
        }

        function selectIPQMethod(method) {
            ipsData.ipqMethod = method;

            // Update radio button selection
            document.querySelectorAll('input[name="ipqMethod"]').forEach(radio => {
                radio.checked = radio.value === method;
                radio.closest('.radio-option').classList.toggle('selected', radio.checked);
            });

            // Show/hide relevant sections
            const externalUpload = document.getElementById('externalUpload');
            const advisorNotes = document.getElementById('advisorNotes');

            externalUpload.classList.toggle('hidden', method !== 'External');
            advisorNotes.classList.toggle('hidden', method !== 'Discussion');
        }

        function loadProgramSelection() {
            // Restore previous selections if any
            if (ipsData.programType) {
                selectProgram(ipsData.programType);
            }
            if (ipsData.ipqMethod) {
                selectIPQMethod(ipsData.ipqMethod);
            }
        }

        async function loadRiskAssessment() {
            const content = document.getElementById('questionnaire-content');

            if (ipsData.ipqMethod === 'Internal') {
                // Use hardcoded questions for now since API is having issues
                const questions = [
                    {
                        "QuestionID": 1,
                        "QuestionText_EN": "What is your current age?",
                        "QuestionText_FR": "Quel est votre âge actuel?",
                        "QuestionType": "MultipleChoice",
                        "AffectsRiskCapacity": 1,
                        "AffectsRiskTolerance": 0,
                        "SortOrder": 1
                    },
                    {
                        "QuestionID": 2,
                        "QuestionText_EN": "How would you describe your investment experience?",
                        "QuestionText_FR": "Comment décririez-vous votre expérience en investissement?",
                        "QuestionType": "MultipleChoice",
                        "AffectsRiskCapacity": 1,
                        "AffectsRiskTolerance": 1,
                        "SortOrder": 2
                    },
                    {
                        "QuestionID": 3,
                        "QuestionText_EN": "What is your annual household income?",
                        "QuestionText_FR": "Quel est votre revenu familial annuel?",
                        "QuestionType": "MultipleChoice",
                        "AffectsRiskCapacity": 1,
                        "AffectsRiskTolerance": 0,
                        "SortOrder": 3
                    },
                    {
                        "QuestionID": 4,
                        "QuestionText_EN": "What is your primary investment time horizon?",
                        "QuestionText_FR": "Quel est votre horizon de placement principal?",
                        "QuestionType": "MultipleChoice",
                        "AffectsRiskCapacity": 0,
                        "AffectsRiskTolerance": 1,
                        "SortOrder": 4
                    },
                    {
                        "QuestionID": 5,
                        "QuestionText_EN": "If your portfolio declined 25% in one year, what would you do?",
                        "QuestionText_FR": "Si votre portefeuille baissait de 25% en un an, que feriez-vous?",
                        "QuestionType": "MultipleChoice",
                        "AffectsRiskCapacity": 0,
                        "AffectsRiskTolerance": 1,
                        "SortOrder": 5
                    }
                ];

                displayQuestionnaire(questions);
            } else if (ipsData.ipqMethod === 'External') {
                content.innerHTML = `
                    <div class="alert alert-info">
                        You have selected to use an external IPQ. Please upload the document and provide the risk assessment results.
                    </div>
                    <div class="card">
                        <h3>External IPQ Results</h3>
                        <div class="form-group">
                            <label for="externalRiskCapacity">Risk Capacity Level</label>
                            <select id="externalRiskCapacity">
                                <option value="">Select Risk Capacity</option>
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="externalRiskTolerance">Risk Tolerance Level</label>
                            <select id="externalRiskTolerance">
                                <option value="">Select Risk Tolerance</option>
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                    </div>
                `;
            } else if (ipsData.ipqMethod === 'Discussion') {
                content.innerHTML = `
                    <div class="alert alert-info">
                        You have selected advisor discussion method. Please provide the risk assessment based on your client discussion.
                    </div>
                    <div class="card">
                        <h3>Risk Assessment Results</h3>
                        <div class="form-group">
                            <label for="discussionRiskCapacity">Risk Capacity Level</label>
                            <select id="discussionRiskCapacity">
                                <option value="">Select Risk Capacity</option>
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="discussionRiskTolerance">Risk Tolerance Level</label>
                            <select id="discussionRiskTolerance">
                                <option value="">Select Risk Tolerance</option>
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="discussionNotes">Discussion Notes</label>
                            <textarea id="discussionNotes" rows="4" placeholder="Please provide details about your discussion with the client..."></textarea>
                        </div>
                    </div>
                `;
            }
        }

        function displayQuestionnaire(questions) {
            const content = document.getElementById('questionnaire-content');

            let html = `
                <div class="alert alert-info">
                    Please complete the Investment Policy Questionnaire to determine the client's risk profile.
                </div>
            `;

            questions.forEach((question, index) => {
                html += `
                    <div class="questionnaire-question" data-question-id="${question.QuestionID}">
                        <div class="question-text">${index + 1}. ${question.QuestionText_EN}</div>
                        <div class="answers-grid" id="answers-${question.QuestionID}">
                            <!-- Answers will be loaded here -->
                        </div>
                    </div>
                `;
            });

            content.innerHTML = html;

            // Load answers for each question
            questions.forEach(question => {
                loadQuestionAnswers(question.QuestionID);
            });
        }

        async function loadQuestionAnswers(questionId) {
            // Use hardcoded answers for now since API is having issues
            const allAnswers = {
                1: [ // Age
                    {"AnswerID": 1, "AnswerText_EN": "Under 30", "Score": 5},
                    {"AnswerID": 2, "AnswerText_EN": "30-40", "Score": 4},
                    {"AnswerID": 3, "AnswerText_EN": "41-50", "Score": 3},
                    {"AnswerID": 4, "AnswerText_EN": "51-60", "Score": 2},
                    {"AnswerID": 5, "AnswerText_EN": "Over 60", "Score": 1}
                ],
                2: [ // Experience
                    {"AnswerID": 6, "AnswerText_EN": "Extensive (15+ years)", "Score": 5},
                    {"AnswerID": 7, "AnswerText_EN": "Considerable (10-15 years)", "Score": 4},
                    {"AnswerID": 8, "AnswerText_EN": "Moderate (5-10 years)", "Score": 3},
                    {"AnswerID": 9, "AnswerText_EN": "Some (2-5 years)", "Score": 2},
                    {"AnswerID": 10, "AnswerText_EN": "Limited (less than 2 years)", "Score": 1}
                ],
                3: [ // Income
                    {"AnswerID": 11, "AnswerText_EN": "Over $300,000", "Score": 5},
                    {"AnswerID": 12, "AnswerText_EN": "$200,000 - $300,000", "Score": 4},
                    {"AnswerID": 13, "AnswerText_EN": "$100,000 - $200,000", "Score": 3},
                    {"AnswerID": 14, "AnswerText_EN": "$50,000 - $100,000", "Score": 2},
                    {"AnswerID": 15, "AnswerText_EN": "Under $50,000", "Score": 1}
                ],
                4: [ // Time Horizon
                    {"AnswerID": 16, "AnswerText_EN": "More than 15 years", "Score": 5},
                    {"AnswerID": 17, "AnswerText_EN": "10-15 years", "Score": 4},
                    {"AnswerID": 18, "AnswerText_EN": "5-10 years", "Score": 3},
                    {"AnswerID": 19, "AnswerText_EN": "2-5 years", "Score": 2},
                    {"AnswerID": 20, "AnswerText_EN": "Less than 2 years", "Score": 1}
                ],
                5: [ // Risk Reaction
                    {"AnswerID": 21, "AnswerText_EN": "Buy more - it's a great opportunity", "Score": 5},
                    {"AnswerID": 22, "AnswerText_EN": "Hold and wait for recovery", "Score": 4},
                    {"AnswerID": 23, "AnswerText_EN": "Sell some to reduce risk", "Score": 3},
                    {"AnswerID": 24, "AnswerText_EN": "Sell most investments", "Score": 2},
                    {"AnswerID": 25, "AnswerText_EN": "Sell everything immediately", "Score": 1}
                ]
            };

            const answers = allAnswers[questionId] || [];
            displayQuestionAnswers(questionId, answers);
        }

        function displayQuestionAnswers(questionId, answers) {
            const container = document.getElementById(`answers-${questionId}`);

            let html = '';
            answers.forEach(answer => {
                html += `
                    <div class="answer-option" onclick="selectAnswer(${questionId}, ${answer.AnswerID}, ${answer.Score})">
                        <input type="radio" name="question-${questionId}" value="${answer.AnswerID}" id="answer-${answer.AnswerID}">
                        <label for="answer-${answer.AnswerID}">${answer.AnswerText_EN}</label>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function selectAnswer(questionId, answerId, score) {
            // Update radio button selection
            document.querySelectorAll(`input[name="question-${questionId}"]`).forEach(radio => {
                radio.checked = radio.value == answerId;
                radio.closest('.answer-option').classList.toggle('selected', radio.checked);
            });

            // Store the answer
            if (!ipsData.answers) {
                ipsData.answers = {};
            }
            ipsData.answers[questionId] = {
                answerId: answerId,
                score: score
            };
        }

        async function loadModelSelection() {
            const content = document.getElementById('model-selection-content');

            // Calculate risk profile first
            calculateRiskProfile();

            // Get the selected program type
            const programType = ipsData.programType || 'UMA';

            let modelSectionHTML = '';
            let infoMessage = '';

            if (programType === 'UMA') {
                infoMessage = 'Select UMA models for the client based on their risk profile and investment objectives.';
                modelSectionHTML = `
                    <div class="card">
                        <h3>Available UMA Models</h3>
                        <div id="models-list">
                            <div class="loading">Loading available models...</div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>Selected Models</h3>
                        <div id="selected-models">
                            <p>No models selected yet.</p>
                        </div>
                        <div class="form-group">
                            <label>Total Allocation: <span id="totalAllocation">0</span>%</label>
                            <div id="allocationWarning" class="alert alert-warning hidden">
                                Total allocation must equal 100%
                            </div>
                        </div>
                    </div>
                `;
            } else if (programType === 'Managed') {
                infoMessage = 'For the Discretionary PM Program (Managed), the portfolio will be managed according to the determined risk profile and investment strategy.';
                modelSectionHTML = `
                    <div class="card">
                        <h3>Investment Strategy</h3>
                        <div class="alert alert-info">
                            <strong>Discretionary Portfolio Management:</strong><br>
                            Your portfolio will be professionally managed based on your risk profile and investment objectives.
                            The portfolio manager will make investment decisions on your behalf according to the established investment policy.
                        </div>
                        <div class="strategy-summary">
                            <h4>Recommended Strategy: ${getRecommendedStrategy()}</h4>
                            <p>Based on your risk assessment, this strategy aligns with your investment objectives and risk tolerance.</p>
                        </div>
                    </div>
                `;
            } else if (programType === 'Non-Managed') {
                infoMessage = 'For the Fee Based Program (Non-Discretionary), you will receive investment recommendations and advice, but retain control over investment decisions.';
                modelSectionHTML = `
                    <div class="card">
                        <h3>Advisory Service</h3>
                        <div class="alert alert-info">
                            <strong>Non-Discretionary Advisory:</strong><br>
                            You will receive professional investment advice and recommendations based on your risk profile.
                            All investment decisions remain under your control, and you will approve each transaction.
                        </div>
                        <div class="advisory-summary">
                            <h4>Recommended Approach: ${getRecommendedStrategy()}</h4>
                            <p>Your advisor will provide ongoing recommendations aligned with this investment approach.</p>
                        </div>
                    </div>
                `;
            }

            content.innerHTML = `
                <div class="alert alert-info">
                    ${infoMessage}
                </div>

                <div class="card">
                    <h3>Risk Profile Summary</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Risk Capacity: <strong>${ipsData.riskCapacityLevel || 'Not determined'}</strong></label>
                        </div>
                        <div class="form-group">
                            <label>Risk Tolerance: <strong>${ipsData.riskToleranceLevel || 'Not determined'}</strong></label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="riskProfileOverride">Override Risk Profile (Optional)</label>
                        <select id="riskProfileOverride">
                            <option value="">Use Calculated Risk Profile</option>
                            <option value="Conservative">Conservative</option>
                            <option value="Balanced">Balanced</option>
                            <option value="Growth">Growth</option>
                        </select>
                    </div>

                    <div class="form-group hidden" id="overrideReason">
                        <label for="overrideReasonText">Reason for Override</label>
                        <textarea id="overrideReasonText" rows="3" placeholder="Please explain why you are overriding the calculated risk profile..."></textarea>
                    </div>
                </div>

                ${modelSectionHTML}
            `;

            // Add event listener for risk profile override
            document.getElementById('riskProfileOverride').addEventListener('change', function() {
                const overrideReason = document.getElementById('overrideReason');
                overrideReason.classList.toggle('hidden', !this.value);
            });

            // Only load models if UMA program is selected
            if (programType === 'UMA') {
                loadAvailableModels();
            }
        }

        function calculateRiskProfile() {
            if (ipsData.ipqMethod === 'Internal' && ipsData.answers) {
                let capacityScore = 0;
                let toleranceScore = 0;

                // Calculate scores based on answers
                Object.values(ipsData.answers).forEach(answer => {
                    // This is simplified - in reality you'd check which questions affect which scores
                    capacityScore += answer.score;
                    toleranceScore += answer.score;
                });

                ipsData.riskCapacityScore = capacityScore;
                ipsData.riskToleranceScore = toleranceScore;

                // Determine risk levels based on scores (simplified logic)
                ipsData.riskCapacityLevel = capacityScore <= 12 ? 'Low' : capacityScore <= 24 ? 'Medium' : 'High';
                ipsData.riskToleranceLevel = toleranceScore <= 15 ? 'Low' : toleranceScore <= 30 ? 'Medium' : 'High';
            } else if (ipsData.ipqMethod === 'External') {
                ipsData.riskCapacityLevel = document.getElementById('externalRiskCapacity')?.value || '';
                ipsData.riskToleranceLevel = document.getElementById('externalRiskTolerance')?.value || '';
            } else if (ipsData.ipqMethod === 'Discussion') {
                ipsData.riskCapacityLevel = document.getElementById('discussionRiskCapacity')?.value || '';
                ipsData.riskToleranceLevel = document.getElementById('discussionRiskTolerance')?.value || '';
            }
        }

        function getRecommendedStrategy() {
            const riskCapacity = ipsData.riskCapacityLevel || 'Medium';
            const riskTolerance = ipsData.riskToleranceLevel || 'Medium';
            const programType = ipsData.programType || 'UMA';

            // Strategy mapping based on risk profile
            const strategies = {
                'UMA': {
                    'Low-Low': 'Conservative UMA Portfolio',
                    'Low-Medium': 'Conservative-Balanced UMA Portfolio',
                    'Low-High': 'Balanced-Conservative UMA Portfolio',
                    'Medium-Low': 'Balanced-Conservative UMA Portfolio',
                    'Medium-Medium': 'Balanced UMA Portfolio',
                    'Medium-High': 'Balanced-Growth UMA Portfolio',
                    'High-Low': 'Growth-Conservative UMA Portfolio',
                    'High-Medium': 'Growth UMA Portfolio',
                    'High-High': 'Aggressive Growth UMA Portfolio'
                },
                'Managed': {
                    'Low-Low': 'Conservative Managed Portfolio',
                    'Low-Medium': 'Conservative-Balanced Managed Portfolio',
                    'Low-High': 'Balanced-Conservative Managed Portfolio',
                    'Medium-Low': 'Balanced-Conservative Managed Portfolio',
                    'Medium-Medium': 'Balanced Managed Portfolio',
                    'Medium-High': 'Balanced-Growth Managed Portfolio',
                    'High-Low': 'Growth-Conservative Managed Portfolio',
                    'High-Medium': 'Growth Managed Portfolio',
                    'High-High': 'Aggressive Growth Managed Portfolio'
                },
                'Non-Managed': {
                    'Low-Low': 'Conservative Advisory Approach',
                    'Low-Medium': 'Conservative-Balanced Advisory Approach',
                    'Low-High': 'Balanced-Conservative Advisory Approach',
                    'Medium-Low': 'Balanced-Conservative Advisory Approach',
                    'Medium-Medium': 'Balanced Advisory Approach',
                    'Medium-High': 'Balanced-Growth Advisory Approach',
                    'High-Low': 'Growth-Conservative Advisory Approach',
                    'High-Medium': 'Growth Advisory Approach',
                    'High-High': 'Aggressive Growth Advisory Approach'
                }
            };

            const key = `${riskCapacity}-${riskTolerance}`;
            return strategies[programType]?.[key] || 'Balanced Portfolio';
        }

        async function loadAvailableModels() {
            try {
                // Try to load models from the database API
                const response = await fetch('/api/models');
                if (response.ok) {
                    const models = await response.json();

                    // Transform the database model format to match our expected format
                    const transformedModels = models.map(model => ({
                        modelId: model.ModelID || model.modelId,
                        englishDescription: model.EnglishDescription || model.englishDescription,
                        frenchDescription: model.FrenchDescription || model.frenchDescription,
                        englishCategory: model.EnglishCategory || model.englishCategory,
                        frenchCategory: model.FrenchCategory || model.frenchCategory,
                        fixedIncome: model.FixedIncomePercentage || model.fixedIncome || 0,
                        equity: model.EquityPercentage || model.equity || 0,
                        alternatives: model.AlternativesPercentage || model.alternatives || 0,
                        programType: model.ProgramType || model.programType || 'UMA',
                        isActive: model.IsActive !== undefined ? model.IsActive : (model.isActive !== undefined ? model.isActive : 1)
                    }));

                    // Filter for active models only
                    const activeModels = transformedModels.filter(model => model.isActive === 1 || model.isActive === true);

                    // Store models globally for use in other functions
                    modelsData = activeModels;
                    displayAvailableModels(activeModels);

                    console.log(`Loaded ${activeModels.length} active models from database`);
                } else {
                    throw new Error('Failed to load models from API');
                }
            } catch (error) {
                console.error('Error loading models from API, using fallback data:', error);

                // Fallback to hardcoded models if API fails
                const fallbackModels = [
                    {
                        "modelId": 1,
                        "englishDescription": "Elite Core plus High Growth",
                        "frenchDescription": "Elite de croissance élevée de base plus",
                        "englishCategory": "Elite Partner Portfolios",
                        "frenchCategory": "Portefeuilles Partenaires Elite",
                        "fixedIncome": 20,
                        "equity": 75,
                        "alternatives": 5,
                        "programType": "UMA",
                        "isActive": 1
                    },
                    {
                        "modelId": 2,
                        "englishDescription": "Elite Core plus Balanced Growth",
                        "frenchDescription": "Elite de croissance équilibrée de base plus",
                        "englishCategory": "Elite Partner Portfolios",
                        "frenchCategory": "Portefeuilles Partenaires Elite",
                        "fixedIncome": 40,
                        "equity": 55,
                        "alternatives": 5,
                        "programType": "UMA",
                        "isActive": 1
                    },
                    {
                        "modelId": 3,
                        "englishDescription": "Elite Core plus Conservative",
                        "frenchDescription": "Elite conservateur de base plus",
                        "englishCategory": "Elite Partner Portfolios",
                        "frenchCategory": "Portefeuilles Partenaires Elite",
                        "fixedIncome": 60,
                        "equity": 35,
                        "alternatives": 5,
                        "programType": "UMA",
                        "isActive": 1
                    }
                ];

                // Store fallback models globally
                modelsData = fallbackModels;
                displayAvailableModels(fallbackModels);

                console.log('Using fallback models due to API error');
            }
        }

        function displayAvailableModels(models) {
            const container = document.getElementById('models-list');

            // Filter models for UMA program - include all active models and mutual funds for UMA selection
            const umaModels = models.filter(model =>
                (model.isActive === true || model.isActive === 1) && (
                    model.programType === 'UMA' ||
                    model.programType === null ||
                    model.programType === '' ||
                    (model.programType && model.programType.includes('Elite')) ||
                    (model.programType && model.programType.includes('Partner')) ||
                    (model.programType && model.programType.includes('Strategic')) ||
                    model.modelType === 'mutual_fund'  // Include all active mutual funds
                )
            );

            console.log(`Total models received: ${models.length}`);
            console.log(`UMA models after filtering: ${umaModels.length}`);
            console.log('UMA models:', umaModels);

            if (umaModels.length === 0) {
                container.innerHTML = '<p>No UMA models available.</p>';
                return;
            }

            // Separate regular models and mutual funds for better organization
            const regularModels = umaModels.filter(model => model.modelType === 'model');
            const mutualFunds = umaModels.filter(model => model.modelType === 'mutual_fund');

            let html = '<div class="models-grid">';

            // Display regular models first
            if (regularModels.length > 0) {
                html += '<div class="models-section"><h3>UMA Models</h3>';
                regularModels.forEach(model => {
                    const isRecommended = isModelRecommended(model);
                    html += `
                        <div class="model-card ${isRecommended ? 'recommended' : ''}" data-model-id="${model.modelId}">
                            <div class="model-header">
                                <h4>${model.englishDescription}</h4>
                                ${isRecommended ? '<span class="recommended-badge">Recommended</span>' : ''}
                            </div>
                            <div class="model-details">
                                <p><strong>Category:</strong> ${model.englishCategory}</p>
                                <div class="allocation-display">
                                    <div class="allocation-item">
                                        <span>Fixed Income:</span> <strong>${model.fixedIncome}%</strong>
                                    </div>
                                    <div class="allocation-item">
                                        <span>Equity:</span> <strong>${model.equity}%</strong>
                                    </div>
                                    <div class="allocation-item">
                                        <span>Alternatives:</span> <strong>${model.alternatives}%</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="model-actions">
                                <input type="number" class="allocation-input" placeholder="%" min="0" max="100"
                                       id="allocation-${model.modelId}" onchange="updateModelAllocation(${model.modelId})">
                                <button class="btn btn-secondary" onclick="addModel(${model.modelId})">Add Model</button>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
            }

            // Display mutual funds
            if (mutualFunds.length > 0) {
                html += '<div class="models-section"><h3>🏦 Mutual Funds</h3>';
                mutualFunds.forEach(fund => {
                    html += `
                        <div class="model-card mutual-fund-card" data-model-id="${fund.modelId}">
                            <div class="model-header">
                                <h4>🏦 ${fund.englishDescription}</h4>
                                <span class="fund-code-badge">${fund.fundCode}</span>
                            </div>
                            <div class="model-details">
                                <p><strong>Category:</strong> ${fund.englishCategory}</p>
                                <p><strong>Asset Class:</strong> ${fund.assetClass || 'Mixed'}</p>
                                <div class="fund-info">
                                    <span class="fund-type">Mutual Fund</span>
                                </div>
                            </div>
                            <div class="model-actions">
                                <input type="number" class="allocation-input" placeholder="%" min="0" max="100"
                                       id="allocation-${fund.modelId}" onchange="updateModelAllocation('${fund.modelId}')">
                                <button class="btn btn-secondary" onclick="addModel('${fund.modelId}')">Add Fund</button>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
            }

            html += '</div>';
            container.innerHTML = html;
        }

        function isModelRecommended(model) {
            // Simple recommendation logic based on risk profile
            const riskLevel = ipsData.riskToleranceLevel;

            if (riskLevel === 'Low' && model.equity <= 30) return true;
            if (riskLevel === 'Medium' && model.equity > 30 && model.equity <= 60) return true;
            if (riskLevel === 'High' && model.equity > 60) return true;

            return false;
        }

        function addModel(modelId) {
            const allocationInput = document.getElementById(`allocation-${modelId}`);
            const allocation = parseFloat(allocationInput.value);

            if (!allocation || allocation <= 0) {
                alert('Please enter a valid allocation percentage.');
                return;
            }

            // Find the model
            const model = modelsData.find(m => m.modelId === modelId);
            if (!model) return;

            // Add to selected models
            if (!ipsData.selectedModels) {
                ipsData.selectedModels = [];
            }

            // Check if model already selected
            const existingIndex = ipsData.selectedModels.findIndex(m => m.modelId === modelId);
            if (existingIndex >= 0) {
                ipsData.selectedModels[existingIndex].allocation = allocation;
            } else {
                ipsData.selectedModels.push({
                    modelId: modelId,
                    model: model,
                    allocation: allocation
                });
            }

            updateSelectedModelsDisplay();
            updateTotalAllocation();
        }

        function updateSelectedModelsDisplay() {
            const container = document.getElementById('selected-models');

            if (!ipsData.selectedModels || ipsData.selectedModels.length === 0) {
                container.innerHTML = '<p>No models selected yet.</p>';
                return;
            }

            let html = '<div class="selected-models-list">';

            ipsData.selectedModels.forEach((selectedModel, index) => {
                html += `
                    <div class="selected-model-item">
                        <div class="model-info">
                            <strong>${selectedModel.model.englishDescription}</strong>
                            <span class="allocation-display">${selectedModel.allocation}%</span>
                        </div>
                        <button class="btn btn-secondary" onclick="removeModel(${index})">Remove</button>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        function removeModel(index) {
            ipsData.selectedModels.splice(index, 1);
            updateSelectedModelsDisplay();
            updateTotalAllocation();
        }

        function updateTotalAllocation() {
            if (!ipsData.selectedModels) {
                document.getElementById('totalAllocation').textContent = '0';
                return;
            }

            const total = ipsData.selectedModels.reduce((sum, model) => sum + model.allocation, 0);
            document.getElementById('totalAllocation').textContent = total.toFixed(1);

            const warning = document.getElementById('allocationWarning');
            warning.classList.toggle('hidden', Math.abs(total - 100) < 0.1);
        }

        async function loadReviewStep() {
            const content = document.getElementById('review-content');

            // Save any pending data from previous steps
            saveCurrentStepData();

            content.innerHTML = `
                <div class="alert alert-info">
                    Please review all information before generating the IPS document.
                </div>

                <div class="review-section">
                    <h3>Client Information</h3>
                    <div class="review-item">
                        <span class="review-label">Client Name:</span>
                        <span class="review-value">${ipsData.clientName || 'Not provided'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Account Number:</span>
                        <span class="review-value">${ipsData.accountNumber || 'Not provided'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Rep Code:</span>
                        <span class="review-value">${ipsData.repCode || 'Not provided'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Advisor Name:</span>
                        <span class="review-value">${ipsData.advisorName || 'Not provided'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Account Type:</span>
                        <span class="review-value">${ipsData.accountType || 'Not provided'}</span>
                    </div>
                </div>

                <div class="review-section">
                    <h3>Program & Risk Assessment</h3>
                    <div class="review-item">
                        <span class="review-label">Program Type:</span>
                        <span class="review-value">${ipsData.programType || 'Not selected'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">IPQ Method:</span>
                        <span class="review-value">${ipsData.ipqMethod || 'Not selected'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Risk Capacity:</span>
                        <span class="review-value">${ipsData.riskCapacityLevel || 'Not determined'}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Risk Tolerance:</span>
                        <span class="review-value">${ipsData.riskToleranceLevel || 'Not determined'}</span>
                    </div>
                    ${ipsData.riskProfileOverride ? `
                    <div class="review-item">
                        <span class="review-label">Risk Profile Override:</span>
                        <span class="review-value">${ipsData.riskProfileOverride}</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Override Reason:</span>
                        <span class="review-value">${ipsData.overrideReason || 'Not provided'}</span>
                    </div>
                    ` : ''}
                </div>

                <div class="review-section">
                    <h3>Selected Models</h3>
                    <div id="review-models">
                        ${generateModelsReview()}
                    </div>
                    <div class="review-item">
                        <span class="review-label">Total Allocation:</span>
                        <span class="review-value">${getTotalAllocation()}%</span>
                    </div>
                </div>

                <div class="review-section">
                    <h3>Generate IPS</h3>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="confirmAccuracy" required>
                            I confirm that all information is accurate and complete
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="confirmCompliance" required>
                            I confirm that this IPS complies with regulatory requirements
                        </label>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-success" onclick="generateIPS()" id="generateBtn" disabled>
                            Generate IPS Document
                        </button>
                        <button class="btn btn-secondary" onclick="saveAsDraft()" style="margin-left: 10px;">
                            Save as Draft
                        </button>
                    </div>
                </div>
            `;

            // Add event listeners for checkboxes
            document.getElementById('confirmAccuracy').addEventListener('change', updateGenerateButton);
            document.getElementById('confirmCompliance').addEventListener('change', updateGenerateButton);
        }

        function generateModelsReview() {
            if (!ipsData.selectedModels || ipsData.selectedModels.length === 0) {
                return '<p>No models selected</p>';
            }

            let html = '<div class="selected-models-review">';
            ipsData.selectedModels.forEach(selectedModel => {
                html += `
                    <div class="review-item">
                        <span class="review-label">${selectedModel.model.englishDescription}:</span>
                        <span class="review-value">${selectedModel.allocation}%</span>
                    </div>
                `;
            });
            html += '</div>';
            return html;
        }

        function getTotalAllocation() {
            if (!ipsData.selectedModels) return 0;
            return ipsData.selectedModels.reduce((sum, model) => sum + model.allocation, 0).toFixed(1);
        }

        function updateGenerateButton() {
            const accuracy = document.getElementById('confirmAccuracy').checked;
            const compliance = document.getElementById('confirmCompliance').checked;
            const generateBtn = document.getElementById('generateBtn');

            generateBtn.disabled = !(accuracy && compliance);
        }

        async function generateIPS() {
            try {
                // Validate all data
                if (!validateIPSData()) {
                    return;
                }

                // Prepare IPS data for submission
                const ipsDocument = {
                    clientName: ipsData.clientName,
                    accountNumber: ipsData.accountNumber,
                    repCode: ipsData.repCode,
                    advisorName: ipsData.advisorName,
                    programType: ipsData.programType,
                    accountType: ipsData.accountType,
                    riskCapacityLevel: ipsData.riskCapacityLevel,
                    riskToleranceLevel: ipsData.riskToleranceLevel,
                    riskProfileOverride: ipsData.riskProfileOverride,
                    overrideReason: ipsData.overrideReason,
                    ipqMethod: ipsData.ipqMethod,
                    selectedModels: ipsData.selectedModels,
                    status: 'Pending_Approval',
                    createdDate: new Date().toISOString()
                };

                // Show generating message
                const generateBtn = document.getElementById('generateBtn');
                const originalText = generateBtn.textContent;
                generateBtn.textContent = 'Generating IPS...';
                generateBtn.disabled = true;

                try {
                    // Submit to server for PDF generation
                    const response = await fetch('/api/ips-documents', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(ipsDocument)
                    });

                    if (response.ok) {
                        // The response is a PDF file, trigger download
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;

                        // Create filename
                        const clientName = ipsDocument.clientName.replace(/[^a-zA-Z0-9]/g, '_');
                        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                        a.download = `IPS_${clientName}_${timestamp}.pdf`;

                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        // Show success message
                        showIPSSuccessMessage('Generated', ipsDocument);

                    } else {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(errorData.error || `Server error: ${response.status}`);
                    }

                } catch (fetchError) {
                    console.error('API Error:', fetchError);

                    // Show error message
                    alert('Error generating IPS PDF: ' + fetchError.message + '\n\nPlease check that the server is running and try again.');
                }

            } catch (error) {
                console.error('Error generating IPS:', error);
                alert('Error generating IPS document: ' + error.message);

                // Reset button
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.textContent = 'Generate IPS Document';
                generateBtn.disabled = false;
            }
        }

        function showIPSSuccessMessage(ipsId, ipsDocument, isDemo = false) {
            const demoNote = isDemo ? '\n\n⚠️ Note: This is a demonstration. The IPS was not saved to the database due to server connectivity issues.' : '';

            const summary = `
🎉 IPS Document Generated Successfully!

📋 Document Details:
• Document ID: ${ipsId}
• Client: ${ipsDocument.clientName}
• Rep Code: ${ipsDocument.repCode}
• Advisor: ${ipsDocument.advisorName}
• Program: ${ipsDocument.programType}
• Account Type: ${ipsDocument.accountType}

🎯 Risk Assessment:
• Risk Capacity: ${ipsDocument.riskCapacityLevel}
• Risk Tolerance: ${ipsDocument.riskToleranceLevel}
• Assessment Method: ${ipsDocument.ipqMethod}

💼 Selected Models:
${ipsDocument.selectedModels ? ipsDocument.selectedModels.map(m =>
    `• ${m.model?.englishDescription || 'Model ' + m.modelId}: ${m.allocation}%`
).join('\n') : '• No models selected (Non-UMA program)'}

📅 Created: ${new Date(ipsDocument.createdDate).toLocaleString()}
📊 Status: ${ipsDocument.status}${demoNote}
            `;

            alert(summary);

            // Reset form or redirect
            if (confirm('Would you like to create another IPS?')) {
                location.reload();
            } else {
                window.location.href = '/';
            }
        }

        async function saveAsDraft() {
            try {
                const ipsDocument = {
                    clientName: ipsData.clientName,
                    accountNumber: ipsData.accountNumber,
                    repCode: ipsData.repCode,
                    advisorName: ipsData.advisorName,
                    programType: ipsData.programType,
                    accountType: ipsData.accountType,
                    riskCapacityLevel: ipsData.riskCapacityLevel,
                    riskToleranceLevel: ipsData.riskToleranceLevel,
                    riskProfileOverride: ipsData.riskProfileOverride,
                    overrideReason: ipsData.overrideReason,
                    ipqMethod: ipsData.ipqMethod,
                    selectedModels: ipsData.selectedModels,
                    status: 'Draft',
                    createdDate: new Date().toISOString()
                };

                try {
                    const response = await fetch('/api/ips-documents?generate_pdf=false', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(ipsDocument)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        alert(`✅ IPS saved as draft successfully!\n\nDraft ID: ${result.ipsId}\nClient: ${ipsDocument.clientName}\nStatus: Draft`);
                    } else {
                        throw new Error('Failed to save draft');
                    }
                } catch (fetchError) {
                    console.error('API Error:', fetchError);

                    // Show draft saved message even if API fails
                    const draftId = 'DRAFT-' + Date.now();
                    alert(`✅ Draft saved locally!\n\nDraft ID: ${draftId}\nClient: ${ipsDocument.clientName}\nStatus: Draft\n\n⚠️ Note: Draft was not saved to database due to server connectivity issues.`);
                }

            } catch (error) {
                console.error('Error saving draft:', error);
                alert('Error saving draft: ' + error.message);
            }
        }

        function validateIPSData() {
            // Check required fields
            if (!ipsData.clientName || !ipsData.repCode || !ipsData.advisorName || !ipsData.accountType) {
                alert('Please complete all required client information fields.');
                return false;
            }

            if (!ipsData.programType || !ipsData.ipqMethod) {
                alert('Please complete program selection.');
                return false;
            }

            if (!ipsData.riskCapacityLevel || !ipsData.riskToleranceLevel) {
                alert('Please complete risk assessment.');
                return false;
            }

            // Only validate model selection for UMA program
            if (ipsData.programType === 'UMA') {
                if (!ipsData.selectedModels || ipsData.selectedModels.length === 0) {
                    alert('Please select at least one UMA model.');
                    return false;
                }

                const totalAllocation = parseFloat(getTotalAllocation());
                if (Math.abs(totalAllocation - 100) > 0.1) {
                    alert('Total model allocation must equal 100%.');
                    return false;
                }
            }

            return true;
        }

        // Prospect Management Functions
        let prospectsData = [];

        async function loadProspects() {
            try {
                const response = await fetch('/api/prospects');
                if (response.ok) {
                    prospectsData = await response.json();
                    updateProspectDropdown();
                } else {
                    console.log('No prospects found or API not available');
                    prospectsData = [];
                }
            } catch (error) {
                console.error('Error loading prospects:', error);
                prospectsData = [];
            }
        }

        function updateProspectDropdown() {
            const prospectSelect = document.getElementById('prospectSelect');
            if (!prospectSelect) return;

            // Clear existing options except the first one
            while (prospectSelect.options.length > 1) {
                prospectSelect.remove(1);
            }

            // Add prospects to dropdown
            prospectsData.forEach(prospect => {
                const option = document.createElement('option');
                option.value = prospect.ProspectID;
                option.textContent = `${prospect.FirstName} ${prospect.LastName} (${prospect.RepCode})`;
                prospectSelect.appendChild(option);
            });
        }

        function loadProspectData() {
            const prospectSelect = document.getElementById('prospectSelect');
            const selectedProspectId = prospectSelect.value;

            if (!selectedProspectId) {
                // Clear form if no prospect selected
                clearClientForm();
                return;
            }

            const prospect = prospectsData.find(p => p.ProspectID == selectedProspectId);
            if (prospect) {
                // Populate form with prospect data
                document.getElementById('clientName').value = `${prospect.FirstName} ${prospect.LastName}`;
                document.getElementById('repCode').value = prospect.RepCode || '';
                document.getElementById('advisorName').value = prospect.AdvisorName || '';
                document.getElementById('accountType').value = prospect.AccountType || '';

                // Store prospect data in ipsData for later use
                ipsData.prospectId = prospect.ProspectID;
                ipsData.prospectEmail = prospect.Email;
                ipsData.prospectPhone = prospect.Phone;
                ipsData.prospectDateOfBirth = prospect.DateOfBirth;
                ipsData.prospectEstimatedInvestment = prospect.EstimatedInvestment;
                ipsData.prospectRiskTolerance = prospect.RiskTolerance;
                ipsData.prospectInvestmentObjective = prospect.InvestmentObjective;
                ipsData.prospectNotes = prospect.Notes;
            }
        }

        function clearClientForm() {
            document.getElementById('clientName').value = '';
            document.getElementById('accountNumber').value = '';
            document.getElementById('repCode').value = '';
            document.getElementById('advisorName').value = '';
            document.getElementById('accountType').value = '';

            // Clear prospect data from ipsData
            delete ipsData.prospectId;
            delete ipsData.prospectEmail;
            delete ipsData.prospectPhone;
            delete ipsData.prospectDateOfBirth;
            delete ipsData.prospectEstimatedInvestment;
            delete ipsData.prospectRiskTolerance;
            delete ipsData.prospectInvestmentObjective;
            delete ipsData.prospectNotes;
        }

        function showAddProspectModal() {
            document.getElementById('addProspectModal').style.display = 'flex';
        }

        function hideAddProspectModal() {
            document.getElementById('addProspectModal').style.display = 'none';
            clearProspectForm();
        }

        function clearProspectForm() {
            document.getElementById('addProspectForm').reset();
        }

        async function saveProspect() {
            // Validate required fields
            const firstName = document.getElementById('prospectFirstName').value.trim();
            const lastName = document.getElementById('prospectLastName').value.trim();

            if (!firstName || !lastName) {
                alert('Please enter both first and last name.');
                return;
            }

            // Collect prospect data
            const prospectData = {
                firstName: firstName,
                lastName: lastName,
                email: document.getElementById('prospectEmail').value.trim(),
                phone: document.getElementById('prospectPhone').value.trim(),
                dateOfBirth: document.getElementById('prospectDateOfBirth').value,
                repCode: document.getElementById('prospectRepCode').value.trim(),
                advisorName: document.getElementById('prospectAdvisorName').value.trim(),
                accountType: document.getElementById('prospectAccountType').value,
                estimatedInvestment: parseFloat(document.getElementById('prospectEstimatedInvestment').value) || null,
                riskTolerance: document.getElementById('prospectRiskTolerance').value,
                investmentObjective: document.getElementById('prospectInvestmentObjective').value,
                notes: document.getElementById('prospectNotes').value.trim(),
                status: 'Active'
            };

            try {
                const response = await fetch('/api/prospects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(prospectData)
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`✅ Prospect saved successfully!\n\nProspect ID: ${result.prospectId}\nName: ${firstName} ${lastName}`);

                    // Reload prospects and update dropdown
                    await loadProspects();

                    // Select the newly created prospect
                    const prospectSelect = document.getElementById('prospectSelect');
                    prospectSelect.value = result.prospectId;
                    loadProspectData();

                    hideAddProspectModal();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `Server error: ${response.status}`);
                }
            } catch (error) {
                console.error('Error saving prospect:', error);
                alert('Error saving prospect: ' + error.message);
            }
        }
    </script>

    <!-- Add Prospect Modal -->
    <div id="addProspectModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Prospect</h3>
                <span class="close" onclick="hideAddProspectModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addProspectForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="prospectFirstName">First Name *</label>
                            <input type="text" id="prospectFirstName" required>
                        </div>
                        <div class="form-group">
                            <label for="prospectLastName">Last Name *</label>
                            <input type="text" id="prospectLastName" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="prospectEmail">Email</label>
                            <input type="email" id="prospectEmail">
                        </div>
                        <div class="form-group">
                            <label for="prospectPhone">Phone</label>
                            <input type="tel" id="prospectPhone">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="prospectDateOfBirth">Date of Birth</label>
                            <input type="date" id="prospectDateOfBirth">
                        </div>
                        <div class="form-group">
                            <label for="prospectRepCode">Rep Code</label>
                            <input type="text" id="prospectRepCode">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="prospectAdvisorName">Advisor Name</label>
                            <input type="text" id="prospectAdvisorName">
                        </div>
                        <div class="form-group">
                            <label for="prospectAccountType">Account Type</label>
                            <select id="prospectAccountType">
                                <option value="">Select Account Type</option>
                                <option value="Individual">Individual</option>
                                <option value="Joint">Joint</option>
                                <option value="Corporate">Corporate</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="prospectEstimatedInvestment">Estimated Investment ($)</label>
                            <input type="number" id="prospectEstimatedInvestment" min="0" step="1000">
                        </div>
                        <div class="form-group">
                            <label for="prospectRiskTolerance">Risk Tolerance</label>
                            <select id="prospectRiskTolerance">
                                <option value="">Select Risk Tolerance</option>
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="prospectInvestmentObjective">Investment Objective</label>
                        <select id="prospectInvestmentObjective">
                            <option value="">Select Investment Objective</option>
                            <option value="Income">Income</option>
                            <option value="Balanced">Balanced</option>
                            <option value="Growth">Growth</option>
                            <option value="Aggressive Growth">Aggressive Growth</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="prospectNotes">Notes</label>
                        <textarea id="prospectNotes" rows="3" placeholder="Additional notes about the prospect..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideAddProspectModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveProspect()">Save Prospect</button>
            </div>
        </div>
    </div>

    <script>
        // Language toggle function
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fr' : 'en';
            updateLanguageDisplay();
        }

        function updateLanguageDisplay() {
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            if (currentLanguage === 'en') {
                englishElements.forEach(el => el.style.display = 'inline');
                frenchElements.forEach(el => el.style.display = 'none');
            } else {
                englishElements.forEach(el => el.style.display = 'none');
                frenchElements.forEach(el => el.style.display = 'inline');
            }
        }

        // Initialize language display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
        });
    </script>
    </div> <!-- Close main-content -->

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the new sidebar when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Generate and insert sidebar HTML
            const sidebar = document.getElementById('sidebar');
            sidebar.innerHTML = generateSidebarHTML();

            // Initialize sidebar functionality
            initializeSidebar();
            initializeLanguageSwitching();
            setActiveNavItem();
            updateLanguageDisplay();
        });
    </script>
</body>
</html>
