#!/usr/bin/env python3
"""
Debug the models API to see the actual data structure
"""

import requests
import json

def debug_models_api():
    """Debug the models API response"""
    try:
        print("🔍 Fetching models from API...")
        response = requests.get('http://localhost:8000/api/models')
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Got {len(models)} models")
            
            # Show first few models with their structure
            for i, model in enumerate(models[:3]):
                print(f"\n📋 Model {i+1}:")
                for key, value in model.items():
                    print(f"  {key}: {value}")
            
            # Check for UMA models specifically
            uma_models = [m for m in models if 
                         m.get('programType') == 'UMA' or 
                         'UMA' in str(m.get('programType', '')) or
                         'Elite' in str(m.get('englishCategory', '')) or
                         m.get('modelType') == 'mutual_fund']
            
            print(f"\n🎯 Found {len(uma_models)} UMA/Elite models:")
            for model in uma_models:
                print(f"  - {model.get('englishDescription', 'N/A')} (Type: {model.get('programType', 'N/A')}, Active: {model.get('isActive', 'N/A')})")
            
            # Check active status
            active_models = [m for m in models if m.get('isActive') == 1 or m.get('IsActive') == 1]
            print(f"\n✅ Found {len(active_models)} active models")
            
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🔍 DEBUGGING MODELS API")
    print("=" * 60)
    
    success = debug_models_api()
    
    print("=" * 60)
    if success:
        print("🎉 DEBUG COMPLETE!")
    else:
        print("💥 DEBUG FAILED!")
    print("=" * 60)
