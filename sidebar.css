/* IA Private Wealth Design System - IPP-UMA System */

:root {
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --light-blue: #dbeafe;
    --background-primary: #f8fafc;
    --background-secondary: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Modern Professional Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(180deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    border-right: 1px solid var(--border-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar.collapsed {
    transform: translateX(-280px);
}

/* Sidebar Header */
.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
}

.brand {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.brand-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.brand-text h2 {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
    color: white;
    letter-spacing: -0.025em;
}

.brand-text p {
    font-size: 0.8rem;
    margin: 2px 0 0 0;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.user-context {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.user-info {
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-role {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Navigation Sections */
.sidebar-nav {
    padding: 20px 0;
    flex: 1;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 32px;
}

.nav-section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-icon {
    font-size: 1rem;
    opacity: 0.7;
}

/* Navigation Items */
.nav-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 3px solid transparent;
    margin: 2px 0;
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #60a5fa;
    transform: translateX(2px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: #fbbf24;
    font-weight: 600;
}

.nav-item.primary {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #60a5fa;
    font-weight: 600;
    color: white;
}

.nav-item.create-action {
    border: 1px dashed rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    margin: 8px 16px;
    padding: 12px 16px;
}

.nav-item.create-action:hover {
    border-color: #60a5fa;
    border-style: solid;
    background: rgba(96, 165, 250, 0.2);
}

.nav-icon {
    margin-right: 12px;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.nav-item:hover .nav-icon,
.nav-item.active .nav-icon {
    opacity: 1;
    transform: scale(1.1);
}

.nav-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.nav-label {
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.2;
}

.nav-description {
    font-size: 0.75rem;
    opacity: 0.7;
    line-height: 1.3;
    color: rgba(255, 255, 255, 0.8);
}

.nav-item:hover .nav-description {
    opacity: 0.9;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    margin-top: auto;
}

.quick-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    justify-content: center;
}

.quick-action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.language-toggle-sidebar {
    display: flex;
    gap: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px;
    border-radius: var(--radius-md);
    justify-content: center;
}

.lang-btn {
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.7);
    flex: 1;
}

.lang-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: var(--shadow-sm);
}

.lang-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

/* Main Content Area */
.main-content {
    margin-left: 280px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 100vh;
    background: var(--background-primary);
}

.main-content.expanded {
    margin-left: 0;
}

/* Top Navigation Bar */
.top-bar {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 20px 35px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
}

.menu-toggle {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 12px;
    cursor: pointer;
    color: white;
    font-size: 1.3em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(59, 130, 246, 0.4);
}

.menu-toggle:active {
    transform: translateY(0);
}

/* Language Toggle */
.language-toggle {
    display: flex;
    gap: 8px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 12px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.language-toggle button {
    padding: 10px 20px;
    background: transparent;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #64748b;
}

.language-toggle button.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.language-toggle button:hover:not(.active) {
    background: #e2e8f0;
    color: #475569;
}

/* Content Area */
.content-area {
    padding: 40px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 40px;
    text-align: center;
    padding: 30px 0;
}

.page-title {
    font-size: 2.5em;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    color: #64748b;
    font-size: 1.2em;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-280px);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-area {
        padding: 30px 25px;
    }

    .page-title {
        font-size: 2.2em;
    }
}

@media (max-width: 768px) {
    .top-bar {
        padding: 15px 20px;
    }

    .content-area {
        padding: 25px 20px;
    }

    .page-header {
        padding: 20px 0;
        margin-bottom: 30px;
    }

    .page-title {
        font-size: 1.8em;
    }

    .page-subtitle {
        font-size: 1.1em;
    }

    .language-toggle {
        gap: 4px;
        padding: 3px;
    }

    .language-toggle button {
        padding: 8px 15px;
        font-size: 0.85em;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 280px;
    }

    .sidebar.collapsed {
        transform: translateX(-280px);
    }

    .content-area {
        padding: 20px 15px;
    }

    .page-title {
        font-size: 1.6em;
    }

    .top-bar {
        padding: 12px 15px;
    }
}

/* Language Switching */
.english-text {
    display: inline;
}

.french-text {
    display: none;
}

body.french .english-text {
    display: none;
}

body.french .french-text {
    display: inline;
}

/* Smooth Animations */
* {
    transition: color 0.2s ease, background-color 0.2s ease;
}

/* Focus States for Accessibility */
.nav-item:focus,
.menu-toggle:focus,
.language-toggle button:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
}

/* Loading States */
.sidebar.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Modern Scrollbar for Main Content */
.main-content::-webkit-scrollbar {
    width: 8px;
}

.main-content::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.main-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
