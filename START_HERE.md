# 🚀 IPP-UMA System - Quick Start Guide

## **How to Start the Prototype**

### **Step 1: Launch the Server**
**Double-click** → `run_ipp_uma_server.bat`

*A command window will open showing the server starting up*

### **Step 2: Open Your Browser**
**Navigate to** → `http://localhost:8000`

*The system will automatically open the Models Viewer page*

### **Step 3: Explore the Features**
**Click on any page** from the sidebar navigation:

- **📊 Models Viewer** - View all investment models
- **💰 Fee Calculator** - Calculate fees for models
- **🔄 Blended Fee Calculator** - Calculate weighted fees
- **📄 IPS Generator** - Create Investment Policy Statements
- **📈 Model Holdings** - View detailed holdings
- **📋 UMA Sales Table** - Analyze UMA sales data
- **➕ Add New Model** - Add new models

---

## **Navigation Tips**

- **Sidebar Menu**: All pages accessible from the left sidebar
- **Language Toggle**: Switch between English/French (top right)
- **Mobile Friendly**: Works on tablets and phones
- **Responsive Design**: Sidebar collapses on smaller screens

---

## **Troubleshooting**

**If the browser doesn't open automatically:**
1. Open your web browser manually
2. Type: `http://localhost:8000`
3. Press Enter

**If you see connection errors:**
1. Make sure `run_ipp_uma_server.bat` is running
2. Check that no other application is using port 8000
3. Try refreshing the browser page

**To stop the server:**
- Close the command window that opened when you ran the batch file

---

## **System Requirements**

- **Python 3.7+** (already installed if the batch file works)
- **Modern web browser** (Chrome, Firefox, Edge, Safari)
- **Windows OS** (for the .bat file)

---

**Ready to start? Double-click `run_ipp_uma_server.bat` now!**
