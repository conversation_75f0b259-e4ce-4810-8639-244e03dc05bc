/* IPP-UMA System Sidebar JavaScript */

// Global variables
let currentLanguage = 'english';

// Modern Sidebar functionality
function initializeSidebar() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (!menuToggle || !sidebar || !mainContent) {
        console.warn('Sidebar elements not found');
        return;
    }

    // Ensure sidebar is visible on desktop by default
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');
    } else {
        // Hide sidebar on mobile/tablet by default
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }

    menuToggle.addEventListener('click', function() {
        if (window.innerWidth <= 1024) {
            sidebar.classList.toggle('show');
        } else {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
    });

    // Close sidebar on mobile when clicking outside
    document.addEventListener('click', function(event) {
        if (window.innerWidth <= 1024) {
            if (!sidebar.contains(event.target) && !menuToggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 1024) {
            sidebar.classList.remove('show');
            // Show sidebar on desktop
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
        } else {
            // Hide sidebar on mobile/tablet
            sidebar.classList.remove('show');
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        }
    });

    // Add smooth loading animation
    setTimeout(() => {
        sidebar.classList.remove('loading');
    }, 100);
}

// Language switching functionality
function initializeLanguageSwitching() {
    // Handle both top bar and sidebar language buttons
    const englishBtns = document.querySelectorAll('#englishBtn, .lang-btn[data-lang="en"]');
    const frenchBtns = document.querySelectorAll('#frenchBtn, .lang-btn[data-lang="fr"]');

    // Add event listeners to English buttons
    englishBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            currentLanguage = 'english';
            updateLanguageDisplay();
            updateSearchPlaceholder();
            // Trigger custom event for page-specific language updates
            document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: 'english' } }));
        });
    });

    // Add event listeners to French buttons
    frenchBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            currentLanguage = 'french';
            updateLanguageDisplay();
            updateSearchPlaceholder();
            // Trigger custom event for page-specific language updates
            document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: 'french' } }));
        });
    });

    // Handle sidebar language buttons specifically
    const sidebarEnglishBtn = document.querySelector('.language-toggle-sidebar #englishBtn');
    const sidebarFrenchBtn = document.querySelector('.language-toggle-sidebar #frenchBtn');

    if (sidebarEnglishBtn && sidebarFrenchBtn) {
        sidebarEnglishBtn.addEventListener('click', function() {
            currentLanguage = 'english';
            updateLanguageDisplay();
            updateSearchPlaceholder();
            document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: 'english' } }));
        });

        sidebarFrenchBtn.addEventListener('click', function() {
            currentLanguage = 'french';
            updateLanguageDisplay();
            updateSearchPlaceholder();
            document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: 'french' } }));
        });
    }
}

function updateLanguageDisplay() {
    // Update button states for top bar
    const englishBtn = document.getElementById('englishBtn');
    const frenchBtn = document.getElementById('frenchBtn');

    if (englishBtn && frenchBtn) {
        englishBtn.classList.toggle('active', currentLanguage === 'english');
        frenchBtn.classList.toggle('active', currentLanguage === 'french');
    }

    // Update sidebar language buttons
    const sidebarEnglishBtn = document.querySelector('.language-toggle-sidebar #englishBtn');
    const sidebarFrenchBtn = document.querySelector('.language-toggle-sidebar #frenchBtn');

    if (sidebarEnglishBtn && sidebarFrenchBtn) {
        sidebarEnglishBtn.classList.toggle('active', currentLanguage === 'english');
        sidebarFrenchBtn.classList.toggle('active', currentLanguage === 'french');
    }

    // Update body class for CSS-based language switching
    document.body.classList.toggle('french', currentLanguage === 'french');

    // Show/hide language-specific text (fallback for older implementations)
    const englishElements = document.querySelectorAll('.english-text');
    const frenchElements = document.querySelectorAll('.french-text');

    englishElements.forEach(el => {
        el.style.display = currentLanguage === 'english' ? 'inline' : 'none';
    });

    frenchElements.forEach(el => {
        el.style.display = currentLanguage === 'french' ? 'inline' : 'none';
    });
}

function updateSearchPlaceholder() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.placeholder = currentLanguage === 'english' ? 'Search models...' : 'Rechercher des modèles...';
    }
}

// Set active navigation item based on current page
function setActiveNavItem() {
    const currentPage = window.location.pathname.split('/').pop() || 'sqlite_viewer.html';
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
        item.classList.remove('active');
        const href = item.getAttribute('href');
        if (href && href.includes(currentPage)) {
            item.classList.add('active');
        }
    });

    // Special case for the main models viewer
    if (currentPage === '' || currentPage === 'sqlite_viewer.html' || currentPage === 'index.html') {
        const modelsViewerNav = document.getElementById('modelsViewerNav');
        if (modelsViewerNav) {
            modelsViewerNav.classList.add('active');
        }
    }
}

// Generate sidebar HTML
function generateSidebarHTML() {
    return `
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item primary" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="#" class="nav-item create-action" id="addModelNav" onclick="openAddModelModal()">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="openAddModelModal()" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    `;
}

// Make functions available globally BEFORE DOMContentLoaded
window.generateSidebarHTML = generateSidebarHTML;
window.initializeSidebar = initializeSidebar;
window.initializeLanguageSwitching = initializeLanguageSwitching;
window.updateLanguageDisplay = updateLanguageDisplay;
window.setActiveNavItem = setActiveNavItem;

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar: DOM loaded, initializing...');

    // First, populate the sidebar with HTML content
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        console.log('Sidebar: Found sidebar element');
        if (!sidebar.innerHTML.trim()) {
            console.log('Sidebar: Populating with HTML...');
            sidebar.innerHTML = generateSidebarHTML();
            console.log('Sidebar: HTML populated, length:', sidebar.innerHTML.length);
        } else {
            console.log('Sidebar: Already has content');
        }
    } else {
        console.error('Sidebar: Element not found!');
    }

    // Then initialize functionality
    try {
        initializeSidebar();
        initializeLanguageSwitching();
        setActiveNavItem();
        updateLanguageDisplay();
        console.log('Sidebar: All functions initialized successfully');
    } catch (error) {
        console.error('Sidebar: Error during initialization:', error);
    }
});

// Export functions for use in other scripts
window.IPPSidebar = {
    initializeSidebar,
    initializeLanguageSwitching,
    updateLanguageDisplay,
    updateSearchPlaceholder,
    setActiveNavItem,
    generateSidebarHTML,
    getCurrentLanguage: () => currentLanguage,
    setCurrentLanguage: (lang) => {
        currentLanguage = lang;
        updateLanguageDisplay();
    }
};
