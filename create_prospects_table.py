import sqlite3

# Create prospects table
conn = sqlite3.connect('ipp_uma_models.db')
cursor = conn.cursor()

# Create table
cursor.execute('''
CREATE TABLE IF NOT EXISTS tblProspects (
    ProspectID INTEGER PRIMARY KEY AUTOINCREMENT,
    FirstName TEXT NOT NULL,
    LastName TEXT NOT NULL,
    Email TEXT,
    Phone TEXT,
    DateOfBirth DATE,
    RepCode TEXT,
    AdvisorName TEXT,
    AccountType TEXT,
    EstimatedInvestment REAL,
    RiskTolerance TEXT,
    InvestmentObjective TEXT,
    Notes TEXT,
    Status TEXT DEFAULT 'Active',
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    LastModified DATETIME DEFAULT CURRENT_TIMESTAMP
)
''')

# Insert sample data
sample_prospects = [
    ('<PERSON>', '<PERSON>', '<EMAIL>', '(*************', '1975-03-15', 'REP001', '<PERSON>', 'Individual', 500000, 'Medium', 'Growth', 'Interested in UMA program for retirement planning', 'Active'),
    ('<PERSON>', '<PERSON><PERSON>', '<EMAIL>', '(*************', '1982-07-22', 'REP002', 'Pierre <PERSON>', 'Joint', 750000, 'High', 'Aggressive Growth', 'Young professional couple looking for long-term growth', 'Active'),
    ('Robert', '<PERSON>', '<EMAIL>', '(*************', '1958-11-08', 'REP003', 'Jennifer Lee', 'Individual', 1200000, 'Low', 'Income', 'Conservative investor nearing retirement', 'Active'),
    ('Lisa', 'Chen', '<EMAIL>', '(*************', '1990-05-12', 'REP001', 'Sarah Johnson', 'Individual', 300000, 'Medium', 'Balanced', 'First-time investor interested in balanced approach', 'Active'),
    ('David', 'Thompson', '<EMAIL>', '(*************', '1965-09-30', 'REP004', 'Michael Brown', 'Corporate', 2000000, 'Medium', 'Growth', 'Corporate account for business investment portfolio', 'Active')
]

cursor.executemany('''
INSERT INTO tblProspects (FirstName, LastName, Email, Phone, DateOfBirth, RepCode, AdvisorName, AccountType, EstimatedInvestment, RiskTolerance, InvestmentObjective, Notes, Status)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
''', sample_prospects)

conn.commit()
print('Prospects table created and sample data inserted successfully!')

# Verify
cursor.execute('SELECT COUNT(*) FROM tblProspects')
count = cursor.fetchone()[0]
print(f'Total prospects: {count}')

conn.close()
