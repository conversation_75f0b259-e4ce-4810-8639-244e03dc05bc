#!/usr/bin/env python3
"""
Migrate Mutual Funds to Models Table

This script adds the necessary columns to tblModels to support mutual funds
and migrates existing mutual funds from tblMutualFunds to tblModels.
"""

import sqlite3
import os

DB_PATH = 'ipp_uma_models.db'

def migrate_mutual_funds():
    """Migrate mutual funds to the models table"""
    print("🔄 Migrating mutual funds to tblModels...")
    
    if not os.path.exists(DB_PATH):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Step 1: Add new columns to tblModels if they don't exist
        print("📝 Adding new columns to tblModels...")
        
        # Check existing columns
        cursor.execute("PRAGMA table_info(tblModels)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        # Add FundCode column
        if 'FundCode' not in existing_columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN FundCode TEXT')
            print("   ✅ Added FundCode column")
        
        # Add FundType column
        if 'FundType' not in existing_columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN FundType TEXT')
            print("   ✅ Added FundType column")
        
        # Add AssetClass column
        if 'AssetClass' not in existing_columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN AssetClass TEXT')
            print("   ✅ Added AssetClass column")
        
        # Add ModelType column to distinguish between models and mutual funds
        if 'ModelType' not in existing_columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN ModelType TEXT DEFAULT "model"')
            print("   ✅ Added ModelType column")
        
        # Update existing records to have ModelType = 'model'
        cursor.execute('UPDATE tblModels SET ModelType = "model" WHERE ModelType IS NULL')
        
        # Add MinimumInvestment if it doesn't exist
        if 'MinimumInvestment' not in existing_columns:
            cursor.execute('ALTER TABLE tblModels ADD COLUMN MinimumInvestment REAL DEFAULT 0')
            print("   ✅ Added MinimumInvestment column")
        
        conn.commit()
        
        # Step 2: Check if tblMutualFunds exists and migrate data
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tblMutualFunds'")
        if cursor.fetchone():
            print("🏦 Migrating existing mutual funds...")
            
            # Get all mutual funds
            cursor.execute("""
                SELECT FundID, FundCode, EnglishName, FrenchName, FundType, AssetClass, 
                       StartDate, EndDate, IsActive
                FROM tblMutualFunds
            """)
            
            mutual_funds = cursor.fetchall()
            print(f"   Found {len(mutual_funds)} mutual funds to migrate")
            
            # Insert each mutual fund as a model
            for fund in mutual_funds:
                # Create category based on fund type
                english_category = f"Mutual Funds - {fund['FundType'] or 'General'}"
                french_category = f"Fonds Mutuels - {fund['FundType'] or 'Général'}"
                
                cursor.execute("""
                    INSERT INTO tblModels (
                        EnglishDescription, FrenchDescription, EnglishCategory, FrenchCategory,
                        StartDate, EndDate, IsActive, FixedIncomePercentage, EquityPercentage,
                        AlternativesPercentage, ProgramType, MinimumInvestment,
                        FundCode, FundType, AssetClass, ModelType
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    fund['EnglishName'],
                    fund['FrenchName'],
                    english_category,
                    french_category,
                    fund['StartDate'],
                    fund['EndDate'],
                    fund['IsActive'],
                    0,  # FixedIncomePercentage
                    0,  # EquityPercentage
                    0,  # AlternativesPercentage
                    'UMA',  # ProgramType
                    0,  # MinimumInvestment
                    fund['FundCode'],
                    fund['FundType'],
                    fund['AssetClass'],
                    'mutual_fund'  # ModelType
                ))
                
                print(f"   ✅ Migrated: {fund['FundCode']} - {fund['EnglishName']}")
            
            conn.commit()
            print(f"✅ Successfully migrated {len(mutual_funds)} mutual funds to tblModels")
        else:
            print("ℹ️  No tblMutualFunds table found, skipping migration")
        
        # Step 3: Verify the migration
        print("🔍 Verifying migration...")
        cursor.execute("SELECT COUNT(*) FROM tblModels WHERE ModelType = 'mutual_fund'")
        mutual_fund_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM tblModels WHERE ModelType = 'model'")
        model_count = cursor.fetchone()[0]
        
        print(f"📊 Final counts:")
        print(f"   Investment Models: {model_count}")
        print(f"   Mutual Funds: {mutual_fund_count}")
        print(f"   Total: {model_count + mutual_fund_count}")
        
        # Show sample mutual funds
        if mutual_fund_count > 0:
            print("\n🏦 Sample migrated mutual funds:")
            cursor.execute("""
                SELECT ModelID, EnglishDescription, FundCode, FundType, AssetClass
                FROM tblModels 
                WHERE ModelType = 'mutual_fund'
                LIMIT 5
            """)
            
            for row in cursor.fetchall():
                print(f"   ID {row[0]}: {row[2]} - {row[1]} ({row[3]}, {row[4]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("🔄 MUTUAL FUNDS TO MODELS MIGRATION")
    print("=" * 60)
    
    if migrate_mutual_funds():
        print("\n🎉 Migration completed successfully!")
        print("✅ Mutual funds are now stored in tblModels")
        print("✅ System simplified to use single table")
        print("✅ Ready to test the updated interface")
    else:
        print("\n❌ Migration failed!")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
