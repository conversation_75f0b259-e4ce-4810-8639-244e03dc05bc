#!/usr/bin/env python3
"""
Simple server starter to debug issues
"""

try:
    print("🔄 Importing modules...")
    import sqlite3
    print("✅ sqlite3 imported")
    
    import json
    print("✅ json imported")
    
    from flask import Flask, request, jsonify, send_from_directory, send_file
    print("✅ Flask imported")
    
    from flask_cors import CORS
    print("✅ CORS imported")
    
    from datetime import datetime
    print("✅ datetime imported")
    
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    print("✅ ReportLab imported")
    
    import io
    print("✅ io imported")
    
    print("🚀 All imports successful, starting server...")
    
    # Import and run the server
    from ipp_uma_server import app
    print("✅ Server app imported")
    
    print("🌐 Starting Flask server on port 8000...")
    app.run(host='0.0.0.0', port=8000, debug=True)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
