@echo off
echo ========================================
echo    IPP-UMA Server Restart
echo ========================================
echo.

echo Stopping any existing server processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    echo Stopping process %%a...
    taskkill /f /pid %%a >nul 2>&1
)

echo Waiting for processes to stop...
timeout /t 3 /nobreak > nul

echo Starting fresh server instance...
start /min python ipp_uma_server.py

echo Waiting for server to initialize...
timeout /t 5 /nobreak > nul

echo Testing server connection...
python -c "import requests; response = requests.get('http://localhost:8000/api/models'); print('✅ Server Status: OK' if response.status_code == 200 else '❌ Server Status: Error')" 2>nul

echo.
echo ========================================
echo    Server restart complete!
echo ========================================
echo.
echo Available interfaces:
echo   🎯 IPS Generator: http://localhost:8000/ips_generator.html
echo   🏠 Main Interface: http://localhost:8000
echo   📊 Pivot Table: http://localhost:8000/uma_pivot_table.html
echo   💰 Fee Calculator: http://localhost:8000/fee_calculator.html
echo.
echo Press any key to exit...
pause > nul
