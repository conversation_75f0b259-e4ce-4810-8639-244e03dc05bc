#!/usr/bin/env python3
import sqlite3

def check_mutual_funds():
    try:
        conn = sqlite3.connect('ipp_uma_models.db')
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tblMutualFunds'")
        if not cursor.fetchone():
            print("❌ tblMutualFunds table does not exist")
            return
        
        # Count active mutual funds
        cursor.execute('SELECT COUNT(*) FROM tblMutualFunds WHERE IsActive = 1')
        active_count = cursor.fetchone()[0]
        print(f"✅ Active mutual funds: {active_count}")
        
        # Show sample funds
        cursor.execute('SELECT FundCode, EnglishName, FundType FROM tblMutualFunds WHERE IsActive = 1 LIMIT 5')
        funds = cursor.fetchall()
        
        if funds:
            print("📋 Sample funds:")
            for fund in funds:
                print(f"   {fund[0]}: {fund[1]} ({fund[2]})")
        else:
            print("⚠️  No active mutual funds found")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_mutual_funds()
