"""
UMA Sales Data Import Script

This script imports UMA sales data from 'UMA sales data.csv' into the SQLite database.
It populates the tblUMASalesData table for use in the pivot table analysis.
"""

import csv
import sqlite3
import os
import sys

# Configuration
DB_PATH = "ipp_uma_models.db"
CSV_PATH = "UMA sales data.csv"

def get_db_connection():
    """Create a connection to the SQLite database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def clear_existing_data(conn):
    """Clear existing UMA sales data"""
    try:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM tblUMASalesData")
        conn.commit()
        print("Cleared existing UMA sales data")
        return True
    except sqlite3.Error as e:
        print(f"Error clearing existing data: {e}")
        return False

def import_uma_sales_data():
    """Import UMA sales data from CSV file"""
    
    # Check if files exist
    if not os.path.exists(DB_PATH):
        print(f"Error: Database file '{DB_PATH}' not found!")
        print("Please run create_new_database.py first to create the database.")
        return False
    
    if not os.path.exists(CSV_PATH):
        print(f"Error: CSV file '{CSV_PATH}' not found!")
        return False
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        # Clear existing data
        if not clear_existing_data(conn):
            return False
        
        # Read and import CSV data
        with open(CSV_PATH, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            cursor = conn.cursor()
            imported_count = 0
            
            for row in reader:
                try:
                    # Clean and prepare data
                    uma_model_code = row['UMA_MODEL_CODE'].strip()
                    uma_model_name = row['UMA_MODEL_NAME'].strip()
                    rep_code = row['REP_CODE'].strip()
                    advisor_full_name = row['ADVISOR_FULL_NAME'].strip()
                    advisor_province = row['ADVISOR_PROVINCE'].strip()
                    advisor_city = row['ADVISOR_CITY'].strip()
                    product_symbol = row['PRODCT_SYMBL'].strip()
                    
                    # Convert AUA to float
                    try:
                        aua = float(row['AUA'])
                    except (ValueError, TypeError):
                        aua = 0.0
                    
                    # Insert into database
                    cursor.execute("""
                        INSERT INTO tblUMASalesData (
                            UMA_MODEL_CODE,
                            UMA_MODEL_NAME,
                            REP_CODE,
                            ADVISOR_FULL_NAME,
                            ADVISOR_PROVINCE,
                            ADVISOR_CITY,
                            PRODCT_SYMBL,
                            AUA
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        uma_model_code,
                        uma_model_name,
                        rep_code,
                        advisor_full_name,
                        advisor_province,
                        advisor_city,
                        product_symbol,
                        aua
                    ))
                    
                    imported_count += 1
                    
                    # Show progress every 100 records
                    if imported_count % 100 == 0:
                        print(f"Imported {imported_count} records...")
                
                except Exception as e:
                    print(f"Error importing row {imported_count + 1}: {e}")
                    print(f"Row data: {row}")
                    continue
            
            # Commit all changes
            conn.commit()
            print(f"\n✅ Successfully imported {imported_count} UMA sales records!")
            
            # Verify the import
            cursor.execute("SELECT COUNT(*) FROM tblUMASalesData")
            total_count = cursor.fetchone()[0]
            print(f"Total records in database: {total_count}")
            
            return True
            
    except Exception as e:
        print(f"Error during import: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def main():
    """Main function"""
    print("=" * 50)
    print("UMA Sales Data Import")
    print("=" * 50)
    print()
    
    print(f"Database: {DB_PATH}")
    print(f"CSV File: {CSV_PATH}")
    print()
    
    if import_uma_sales_data():
        print("\n🎉 Import completed successfully!")
        print("\nYou can now use the UMA Pivot Table to analyze the data.")
        print("Go to: http://localhost:8000/uma_pivot_table.html")
    else:
        print("\n❌ Import failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
