#!/usr/bin/env python3
import requests
import json

def test_models_api():
    try:
        response = requests.get('http://localhost:8000/api/models')
        if response.status_code == 200:
            models = response.json()
            
            # Count different types
            regular_models = [m for m in models if m.get('sourceType') == 'model']
            mutual_funds = [m for m in models if m.get('sourceType') == 'mutual_fund']
            
            print(f"✅ API Response successful")
            print(f"📊 Total models returned: {len(models)}")
            print(f"🏢 Regular models: {len(regular_models)}")
            print(f"🏦 Mutual funds: {len(mutual_funds)}")
            
            if mutual_funds:
                print("\n🏦 Sample mutual funds from API:")
                for fund in mutual_funds[:3]:
                    print(f"   ID: {fund['modelId']}")
                    print(f"   Name: {fund['englishDescription']}")
                    print(f"   Category: {fund['englishCategory']}")
                    print(f"   Fund Code: {fund.get('fundCode', 'N/A')}")
                    print()
            else:
                print("⚠️  No mutual funds found in API response")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Connection Error: {e}")

if __name__ == "__main__":
    test_models_api()
