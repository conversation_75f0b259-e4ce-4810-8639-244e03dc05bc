<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Analytics - IPP-UMA System</title>



    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Main content with sidebar */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top bar */
        .top-bar {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px 35px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-bar h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .top-bar .language-toggle button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .top-bar .language-toggle button:hover {
            background: #3b82f6;
        }

        /* Language display - handled by sidebar.css */

        /* Ensure main sidebar has priority over pivot styles */
        #sidebar.sidebar {
            z-index: 1000 !important;
            position: fixed !important;
            left: 0 !important;
            top: 0 !important;
            width: 280px !important;
            height: 100vh !important;
        }

        /* Page-specific styles */
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .pivot-header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .pivot-content {
            display: flex;
            min-height: 600px;
        }

        .pivot-controls-sidebar {
            width: 300px;
            background-color: #ecf0f1;
            padding: 20px;
            border-right: 1px solid #bdc3c7;
        }

        .pivot-main-content {
            flex: 1;
            padding: 20px;
        }

        .dimension-group {
            margin-bottom: 20px;
        }

        .dimension-group h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 14px;
            text-transform: uppercase;
        }

        .dimension-item {
            background-color: #3498db;
            color: white;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            cursor: move;
            user-select: none;
            transition: background-color 0.2s;
        }

        .dimension-item:hover {
            background-color: #2980b9;
        }

        .drop-zone {
            min-height: 50px;
            border: 2px dashed #bdc3c7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            background-color: #f8f9fa;
            transition: all 0.2s;
        }

        .drop-zone.drag-over {
            border-color: #3498db;
            background-color: #e3f2fd;
        }

        .drop-zone h4 {
            margin: 0 0 10px 0;
            color: #7f8c8d;
            font-size: 12px;
            text-transform: uppercase;
        }

        .dropped-item {
            background-color: #27ae60;
            color: white;
            padding: 6px 10px;
            margin: 3px;
            border-radius: 3px;
            display: inline-block;
            cursor: pointer;
        }

        .dropped-item:hover {
            background-color: #229954;
        }

        .pivot-table {
            margin-top: 20px;
            overflow-x: auto;
        }

        .pivot-table table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
        }

        .pivot-table th,
        .pivot-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .pivot-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }

        .pivot-table .number {
            text-align: right;
            font-family: 'Courier New', monospace;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error {
            background-color: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .controls .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        .controls .btn:hover {
            background-color: #2980b9;
        }

        .controls .btn-secondary {
            background-color: #95a5a6;
        }

        .controls .btn-secondary:hover {
            background-color: #7f8c8d;
        }
    </style>

    <!-- Import sidebar CSS AFTER embedded styles to ensure proper precedence -->
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <div class="brand">
                <div class="brand-icon">🏛️</div>
                <div class="brand-text">
                    <h2>IPP-UMA</h2>
                    <p>
                        <span class="english-text">Investment Platform</span>
                        <span class="french-text">Plateforme d'Investissement</span>
                    </p>
                </div>
            </div>
            <div class="user-context">
                <div class="user-info">
                    <span class="user-role">
                        <span class="english-text">Portfolio Manager</span>
                        <span class="french-text">Gestionnaire de Portefeuille</span>
                    </span>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">🏠</i>
                    <span class="english-text">Dashboard</span>
                    <span class="french-text">Tableau de Bord</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="dashboardNav">
                    <i class="nav-icon">📊</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Overview</span>
                            <span class="french-text">Aperçu</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">System overview & quick access</span>
                            <span class="french-text">Aperçu système & accès rapide</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Portfolio Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">💼</i>
                    <span class="english-text">Portfolio Management</span>
                    <span class="french-text">Gestion de Portefeuille</span>
                </div>
                <a href="sqlite_viewer.html" class="nav-item" id="modelsViewerNav">
                    <i class="nav-icon">🎯</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Models</span>
                            <span class="french-text">Modèles de Portefeuille</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage investment models</span>
                            <span class="french-text">Gérer les modèles d'investissement</span>
                        </span>
                    </div>
                </a>
                <a href="model_holdings_viewer.html" class="nav-item" id="modelHoldingsNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Holdings & Allocations</span>
                            <span class="french-text">Titres et Allocations</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">View detailed portfolio holdings</span>
                            <span class="french-text">Voir les titres détaillés</span>
                        </span>
                    </div>
                </a>
                <a href="add_model.html" class="nav-item create-action" id="addModelNav">
                    <i class="nav-icon">➕</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Create Portfolio Model</span>
                            <span class="french-text">Créer un Modèle</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Add new investment model</span>
                            <span class="french-text">Ajouter un nouveau modèle</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Client Services Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">👥</i>
                    <span class="english-text">Client Services</span>
                    <span class="french-text">Services Clients</span>
                </div>
                <a href="ips_generator.html" class="nav-item" id="ipsGeneratorNav">
                    <i class="nav-icon">📋</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Investment Policy Statements</span>
                            <span class="french-text">Énoncés de Politique</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Generate client IPS documents</span>
                            <span class="french-text">Générer des documents IPS</span>
                        </span>
                    </div>
                </a>
                <a href="fee_calculator.html" class="nav-item" id="feeCalculatorNav">
                    <i class="nav-icon">💰</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Fee Calculator</span>
                            <span class="french-text">Calculateur de Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Calculate investment fees</span>
                            <span class="french-text">Calculer les frais</span>
                        </span>
                    </div>
                </a>
                <a href="blended_fee_calculator.html" class="nav-item" id="blendedFeeCalculatorNav">
                    <i class="nav-icon">🔄</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Portfolio Fee Analysis</span>
                            <span class="french-text">Analyse des Frais</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Blended fee calculations</span>
                            <span class="french-text">Calculs de frais mixtes</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Analytics & Reporting Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">📊</i>
                    <span class="english-text">Analytics & Reporting</span>
                    <span class="french-text">Analyses et Rapports</span>
                </div>
                <a href="uma_pivot_table.html" class="nav-item active" id="pivotTableNav">
                    <i class="nav-icon">📈</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Sales Analytics</span>
                            <span class="french-text">Analyses des Ventes</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">UMA sales performance data</span>
                            <span class="french-text">Données de performance</span>
                        </span>
                    </div>
                </a>
                <a href="uma_pivot_table.html" class="nav-item" id="dataExplorerNav">
                    <i class="nav-icon">🔍</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Explorer</span>
                            <span class="french-text">Explorateur de Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Interactive data analysis</span>
                            <span class="french-text">Analyse interactive</span>
                        </span>
                    </div>
                </a>
            </div>

            <!-- Administration Section -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="section-icon">⚙️</i>
                    <span class="english-text">Administration</span>
                    <span class="french-text">Administration</span>
                </div>
                <a href="mutual_funds_manager.html" class="nav-item" id="mutualFundsNav">
                    <i class="nav-icon">🏦</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Mutual Funds Manager</span>
                            <span class="french-text">Gestionnaire de Fonds Mutuels</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Manage mutual fund data & holdings</span>
                            <span class="french-text">Gérer les fonds mutuels</span>
                        </span>
                    </div>
                </a>
                <a href="mutual_funds_manager.html" class="nav-item" id="dataManagementNav">
                    <i class="nav-icon">🗃️</i>
                    <div class="nav-content">
                        <span class="nav-label">
                            <span class="english-text">Data Management</span>
                            <span class="french-text">Gestion des Données</span>
                        </span>
                        <span class="nav-description">
                            <span class="english-text">Import/export & data tools</span>
                            <span class="french-text">Outils d'import/export</span>
                        </span>
                    </div>
                </a>
            </div>
        </nav>

        <!-- Quick Actions Footer -->
        <div class="sidebar-footer">
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="window.location.href='add_model.html'" title="Quick Add Model">
                    <i>➕</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='ips_generator.html'" title="New IPS">
                    <i>📄</i>
                </button>
                <button class="quick-action-btn" onclick="window.location.href='fee_calculator.html'" title="Calculate Fees">
                    <i>💰</i>
                </button>
            </div>
            <div class="language-toggle-sidebar">
                <button id="englishBtn" class="lang-btn active">EN</button>
                <button id="frenchBtn" class="lang-btn">FR</button>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content" id="mainContent">
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">☰</button>
            <h1>
                <span class="english-text">Sales Analytics</span>
                <span class="french-text">Analyses des Ventes</span>
            </h1>
            <div class="language-toggle">
                <button id="topEnglishBtn" class="active">English</button>
                <button id="topFrenchBtn">Français</button>
            </div>
        </div>

        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">
                    <span class="english-text">UMA Sales Data Pivot Table</span>
                    <span class="french-text">Tableau croisé des données de vente UMA</span>
                </h1>
                <p class="page-subtitle">
                    <span class="english-text">Drag dimensions to create your custom pivot table analysis</span>
                    <span class="french-text">Faites glisser les dimensions pour créer votre analyse de tableau croisé personnalisée</span>
                </p>
            </div>

            <div class="container">
                <div class="pivot-header">
                    <h1>
                        <span class="english-text">UMA Sales Data Pivot Table</span>
                        <span class="french-text">Tableau croisé des données de vente UMA</span>
                    </h1>
                    <p>
                        <span class="english-text">Drag dimensions to create your custom pivot table analysis</span>
                        <span class="french-text">Faites glisser les dimensions pour créer votre analyse de tableau croisé personnalisée</span>
                    </p>
                </div>

                <div class="pivot-content">
                    <div class="pivot-controls-sidebar">
                <div class="dimension-group">
                    <h3>Available Dimensions</h3>
                    <div class="dimension-item" draggable="true" data-field="UMA_MODEL_CODE">Model Code</div>
                    <div class="dimension-item" draggable="true" data-field="UMA_MODEL_NAME">Model Name</div>
                    <div class="dimension-item" draggable="true" data-field="ADVISOR_FULL_NAME">Advisor Name</div>
                    <div class="dimension-item" draggable="true" data-field="ADVISOR_PROVINCE">Province</div>
                    <div class="dimension-item" draggable="true" data-field="ADVISOR_CITY">City</div>
                    <div class="dimension-item" draggable="true" data-field="REP_CODE">Rep Code</div>
                    <div class="dimension-item" draggable="true" data-field="PRODCT_SYMBL">Product Symbol</div>
                </div>

                <div class="dimension-group">
                    <h3>Row Dimensions</h3>
                    <div class="drop-zone" id="row-dimensions">
                        <h4>Drop dimensions here for rows</h4>
                    </div>
                </div>

                <div class="dimension-group">
                    <h3>Column Dimensions</h3>
                    <div class="drop-zone" id="column-dimensions">
                        <h4>Drop dimensions here for columns</h4>
                    </div>
                </div>

                <div class="controls">
                    <button class="btn" onclick="generatePivotTable()">Generate Pivot Table</button>
                    <button class="btn btn-secondary" onclick="clearAll()">Clear All</button>
                </div>
            </div>

                    <div class="pivot-main-content">
                        <div id="pivot-result">
                            <div class="loading">
                                <p>
                                    <span class="english-text">Configure your pivot table using the dimensions on the left, then click "Generate Pivot Table"</span>
                                    <span class="french-text">Configurez votre tableau croisé en utilisant les dimensions à gauche, puis cliquez sur "Générer le tableau croisé"</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

    <script>
        let salesData = [];
        let rowDimensions = [];
        let columnDimensions = [];

        // Load data when page loads
        window.onload = function() {
            loadSalesData();
        };

        async function loadSalesData() {
            try {
                const response = await fetch('/api/uma-sales-data');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                salesData = await response.json();
                console.log(`Loaded ${salesData.length} sales records`);
            } catch (error) {
                console.error('Error loading sales data:', error);
                document.getElementById('pivot-result').innerHTML =
                    `<div class="error">Error loading data: ${error.message}</div>`;
            }
        }

        // Drag and drop functionality
        document.addEventListener('DOMContentLoaded', function() {
            setupDragAndDrop();
        });

        function setupDragAndDrop() {
            // Make dimension items draggable
            const dimensionItems = document.querySelectorAll('.dimension-item');
            dimensionItems.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
            });

            // Setup drop zones
            const dropZones = document.querySelectorAll('.drop-zone');
            dropZones.forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('drop', handleDrop);
                zone.addEventListener('dragenter', handleDragEnter);
                zone.addEventListener('dragleave', handleDragLeave);
            });
        }

        function handleDragStart(e) {
            e.dataTransfer.setData('text/plain', JSON.stringify({
                field: e.target.dataset.field,
                text: e.target.textContent
            }));
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragEnter(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');

            const data = JSON.parse(e.dataTransfer.getData('text/plain'));
            const dropZone = e.target.closest('.drop-zone');

            // Create dropped item
            const droppedItem = document.createElement('div');
            droppedItem.className = 'dropped-item';
            droppedItem.textContent = data.text;
            droppedItem.dataset.field = data.field;
            droppedItem.onclick = function() {
                this.remove();
                updateDimensions();
            };

            dropZone.appendChild(droppedItem);
            updateDimensions();
        }

        function updateDimensions() {
            // Update row dimensions
            const rowZone = document.getElementById('row-dimensions');
            rowDimensions = Array.from(rowZone.querySelectorAll('.dropped-item'))
                .map(item => item.dataset.field);

            // Update column dimensions
            const colZone = document.getElementById('column-dimensions');
            columnDimensions = Array.from(colZone.querySelectorAll('.dropped-item'))
                .map(item => item.dataset.field);
        }

        function clearAll() {
            document.querySelectorAll('.dropped-item').forEach(item => item.remove());
            rowDimensions = [];
            columnDimensions = [];
            document.getElementById('pivot-result').innerHTML =
                '<div class="loading"><p>Configure your pivot table using the dimensions on the left, then click "Generate Pivot Table"</p></div>';
        }

        function generatePivotTable() {
            if (salesData.length === 0) {
                document.getElementById('pivot-result').innerHTML =
                    '<div class="error">No data available. Please ensure the data is loaded.</div>';
                return;
            }

            if (rowDimensions.length === 0 && columnDimensions.length === 0) {
                document.getElementById('pivot-result').innerHTML =
                    '<div class="error">Please add at least one dimension to rows or columns.</div>';
                return;
            }

            const pivotData = createPivotTable(salesData, rowDimensions, columnDimensions);
            displayPivotTable(pivotData);
        }

        function createPivotTable(data, rows, cols) {
            const result = {};
            const columnValues = new Set();

            // First pass: collect all unique column values and aggregate data
            data.forEach(record => {
                const rowKey = rows.map(dim => record[dim] || '(blank)').join(' | ');
                const colKey = cols.length > 0 ? cols.map(dim => record[dim] || '(blank)').join(' | ') : 'Total';

                if (cols.length > 0) {
                    columnValues.add(colKey);
                }

                if (!result[rowKey]) {
                    result[rowKey] = {};
                }

                if (!result[rowKey][colKey]) {
                    result[rowKey][colKey] = 0;
                }

                result[rowKey][colKey] += parseFloat(record.AUA) || 0;
            });

            return {
                data: result,
                columns: cols.length > 0 ? Array.from(columnValues).sort() : ['Total']
            };
        }

        function displayPivotTable(pivotData) {
            const { data, columns } = pivotData;

            let html = '<div class="pivot-table"><table>';

            // Header row
            html += '<thead><tr>';
            html += '<th>Dimensions</th>';
            columns.forEach(col => {
                html += `<th>${col}</th>`;
            });
            html += '<th>Grand Total</th>';
            html += '</tr></thead>';

            // Data rows
            html += '<tbody>';
            let grandTotal = 0;

            Object.keys(data).sort().forEach(rowKey => {
                html += '<tr>';
                html += `<td><strong>${rowKey}</strong></td>`;

                let rowTotal = 0;
                columns.forEach(col => {
                    const value = data[rowKey][col] || 0;
                    rowTotal += value;
                    html += `<td class="number">$${value.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>`;
                });

                grandTotal += rowTotal;
                html += `<td class="number"><strong>$${rowTotal.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong></td>`;
                html += '</tr>';
            });

            // Total row
            html += '<tr style="background-color: #f8f9fa; font-weight: bold;">';
            html += '<td><strong>Grand Total</strong></td>';

            columns.forEach(col => {
                let colTotal = 0;
                Object.keys(data).forEach(rowKey => {
                    colTotal += data[rowKey][col] || 0;
                });
                html += `<td class="number">$${colTotal.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>`;
            });

            html += `<td class="number"><strong>$${grandTotal.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong></td>`;
            html += '</tr>';

            html += '</tbody></table></div>';

            // Add summary
            html += `<div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>Summary:</strong> ${Object.keys(data).length} rows, ${columns.length} columns,
                Grand Total: $${grandTotal.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
            </div>`;

            document.getElementById('pivot-result').innerHTML = html;
        }

        // Language toggle function
        let currentLanguage = 'en';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fr' : 'en';
            updateLanguageDisplay();
        }

        function updateLanguageDisplay() {
            const englishElements = document.querySelectorAll('.english-text');
            const frenchElements = document.querySelectorAll('.french-text');

            if (currentLanguage === 'en') {
                englishElements.forEach(el => el.style.display = 'inline');
                frenchElements.forEach(el => el.style.display = 'none');
            } else {
                englishElements.forEach(el => el.style.display = 'none');
                frenchElements.forEach(el => el.style.display = 'inline');
            }
        }

        // Initialize language display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
        });
    </script>
        </div>
    </div>

    <!-- Load external sidebar JavaScript -->
    <script src="sidebar.js"></script>
    <script>
        // Initialize the sidebar functionality (HTML is now inline)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar functionality
            if (typeof initializeSidebar === 'function') {
                initializeSidebar();
            }
            if (typeof initializeLanguageSwitching === 'function') {
                initializeLanguageSwitching();
            }
            if (typeof setActiveNavItem === 'function') {
                setActiveNavItem();
            }
            if (typeof updateLanguageDisplay === 'function') {
                updateLanguageDisplay();
            }
        });
    </script>
</body>
</html>
