"""
Add Prospects Table to IPP-UMA Database

This script adds the tblProspects table to the existing database
for managing prospect information in the IPS generator.
"""

import sqlite3
import os

# Configuration
DB_PATH = "ipp_uma_models.db"

def get_db_connection():
    """Create a connection to the SQLite database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def create_prospects_table():
    """Create the prospects table"""
    conn = get_db_connection()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='tblProspects'
        """)
        
        if cursor.fetchone():
            print("tblProspects table already exists!")
            return True

        # Create the prospects table
        cursor.execute("""
            CREATE TABLE tblProspects (
                ProspectID INTEGER PRIMARY KEY AUTOINCREMENT,
                FirstName TEXT NOT NULL,
                LastName TEXT NOT NULL,
                Email TEXT,
                Phone TEXT,
                DateOfBirth DATE,
                RepCode TEXT,
                AdvisorName TEXT,
                AccountType TEXT,
                EstimatedInvestment REAL,
                RiskTolerance TEXT,
                InvestmentObjective TEXT,
                Notes TEXT,
                Status TEXT DEFAULT 'Active',
                CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                LastModified DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create some sample prospects
        sample_prospects = [
            {
                'firstName': 'John',
                'lastName': 'Smith',
                'email': '<EMAIL>',
                'phone': '(*************',
                'dateOfBirth': '1975-03-15',
                'repCode': 'REP001',
                'advisorName': 'Sarah Johnson',
                'accountType': 'Individual',
                'estimatedInvestment': 500000,
                'riskTolerance': 'Medium',
                'investmentObjective': 'Growth',
                'notes': 'Interested in UMA program for retirement planning',
                'status': 'Active'
            },
            {
                'firstName': 'Marie',
                'lastName': 'Dubois',
                'email': '<EMAIL>',
                'phone': '(*************',
                'dateOfBirth': '1982-07-22',
                'repCode': 'REP002',
                'advisorName': 'Pierre Martin',
                'accountType': 'Joint',
                'estimatedInvestment': 750000,
                'riskTolerance': 'High',
                'investmentObjective': 'Aggressive Growth',
                'notes': 'Young professional couple looking for long-term growth',
                'status': 'Active'
            },
            {
                'firstName': 'Robert',
                'lastName': 'Wilson',
                'email': '<EMAIL>',
                'phone': '(*************',
                'dateOfBirth': '1958-11-08',
                'repCode': 'REP003',
                'advisorName': 'Jennifer Lee',
                'accountType': 'Individual',
                'estimatedInvestment': 1200000,
                'riskTolerance': 'Low',
                'investmentObjective': 'Income',
                'notes': 'Conservative investor nearing retirement',
                'status': 'Active'
            },
            {
                'firstName': 'Lisa',
                'lastName': 'Chen',
                'email': '<EMAIL>',
                'phone': '(*************',
                'dateOfBirth': '1990-05-12',
                'repCode': 'REP001',
                'advisorName': 'Sarah Johnson',
                'accountType': 'Individual',
                'estimatedInvestment': 300000,
                'riskTolerance': 'Medium',
                'investmentObjective': 'Balanced',
                'notes': 'First-time investor interested in balanced approach',
                'status': 'Active'
            },
            {
                'firstName': 'David',
                'lastName': 'Thompson',
                'email': '<EMAIL>',
                'phone': '(*************',
                'dateOfBirth': '1965-09-30',
                'repCode': 'REP004',
                'advisorName': 'Michael Brown',
                'accountType': 'Corporate',
                'estimatedInvestment': 2000000,
                'riskTolerance': 'Medium',
                'investmentObjective': 'Growth',
                'notes': 'Corporate account for business investment portfolio',
                'status': 'Active'
            }
        ]

        # Insert sample prospects
        for prospect in sample_prospects:
            cursor.execute("""
                INSERT INTO tblProspects (
                    FirstName, LastName, Email, Phone, DateOfBirth, RepCode, AdvisorName,
                    AccountType, EstimatedInvestment, RiskTolerance, InvestmentObjective,
                    Notes, Status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                prospect['firstName'],
                prospect['lastName'],
                prospect['email'],
                prospect['phone'],
                prospect['dateOfBirth'],
                prospect['repCode'],
                prospect['advisorName'],
                prospect['accountType'],
                prospect['estimatedInvestment'],
                prospect['riskTolerance'],
                prospect['investmentObjective'],
                prospect['notes'],
                prospect['status']
            ))

        conn.commit()
        print("✅ Successfully created tblProspects table and added sample data!")
        
        # Verify the creation
        cursor.execute("SELECT COUNT(*) FROM tblProspects")
        count = cursor.fetchone()[0]
        print(f"Total prospects in database: {count}")
        
        return True

    except Exception as e:
        print(f"Error creating prospects table: {e}")
        conn.rollback()
        return False

    finally:
        conn.close()

def main():
    """Main function"""
    print("Adding prospects table to IPP-UMA database...")
    
    # Check if database exists
    if not os.path.exists(DB_PATH):
        print(f"Error: Database file '{DB_PATH}' not found!")
        print("Please run create_new_database.py first to create the database.")
        return
    
    # Create the prospects table
    if create_prospects_table():
        print("\n🎉 Prospects table setup completed successfully!")
        print("You can now use the prospect management features in the IPS generator.")
    else:
        print("\n❌ Failed to create prospects table.")

if __name__ == '__main__':
    main()
