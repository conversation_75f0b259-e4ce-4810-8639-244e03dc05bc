#!/usr/bin/env python3
"""
Test the IPS PDF endpoint specifically to diagnose download issues
"""

import requests
import json

def test_ips_pdf_endpoint():
    """Test the IPS PDF generation endpoint"""
    
    # Sample IPS data
    ips_data = {
        "clientName": "Test Client",
        "advisorName": "Test Advisor",
        "riskProfile": "Moderate",
        "investmentObjective": "Growth",
        "timeHorizon": "Long-term",
        "investmentAmount": 100000,
        "selectedModels": [
            {
                "modelId": 1,
                "englishDescription": "Elite Core plus High Growth",
                "allocation": 100
            }
        ],
        "programType": "UMA",
        "status": "Generated",
        "createdDate": "2025-06-03T13:00:00.000Z"
    }
    
    try:
        print("🧪 Testing IPS PDF endpoint...")
        print(f"📤 Sending request to: http://localhost:8000/api/ips-documents")
        
        response = requests.post(
            'http://localhost:8000/api/ips-documents',
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/pdf'
            },
            json=ips_data,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        print(f"📄 Content Length: {len(response.content)} bytes")
        print(f"🔍 Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            # Check if it's actually a PDF
            if response.content.startswith(b'%PDF'):
                print("✅ Response is a valid PDF!")
                
                # Save the PDF
                filename = "test_endpoint_response.pdf"
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"💾 PDF saved as: {filename}")
                return True
            else:
                print("❌ Response is not a PDF!")
                print(f"🔍 First 200 bytes: {response.content[:200]}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response text: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🧪 IPS PDF ENDPOINT TEST")
    print("=" * 60)
    
    success = test_ips_pdf_endpoint()
    
    print("=" * 60)
    if success:
        print("🎉 PDF ENDPOINT WORKS!")
    else:
        print("💥 PDF ENDPOINT FAILED!")
    print("=" * 60)
